import { HolidayUtils } from '../../../../lib/util/holidayUtils';
import { HOLIDAY_TYPES } from '../../../../lib/constants/holidayConstants';
import moment from 'moment-timezone';

// Mock moment-timezone
jest.mock('moment-timezone', () => {
    const actualMoment = jest.requireActual('moment-timezone');
    const mockMoment = jest.fn((input, timezone) => {
        if (timezone) {
            return actualMoment.tz(input, timezone);
        }
        return actualMoment(input);
    });

    // Copy all methods from actual moment
    Object.setPrototypeOf(mockMoment, actualMoment);
    Object.assign(mockMoment, actualMoment);

    mockMoment.tz = actualMoment.tz;

    return mockMoment;
});

describe('HolidayUtils', () => {
    describe('getHolidaysInRange', () => {
        let mockOrg;
        const timezone = 'America/Chicago';

        beforeEach(() => {
            mockOrg = {
                getHolidays: jest.fn(),
                getTimezone: jest.fn(() => timezone)
            };
        });

        it('should return empty object when no holidays exist', () => {
            mockOrg.getHolidays.mockReturnValue([]);

            const result = HolidayUtils.getHolidaysInRange(1000, 2000, mockOrg);

            expect(result).toEqual({});
        });

        it('should return empty object when holidays is null', () => {
            mockOrg.getHolidays.mockReturnValue(null);

            const result = HolidayUtils.getHolidaysInRange(1000, 2000, mockOrg);

            expect(result).toEqual({});
        });

        it('should return empty object when holidays is undefined', () => {
            mockOrg.getHolidays.mockReturnValue(undefined);

            const result = HolidayUtils.getHolidaysInRange(1000, 2000, mockOrg);

            expect(result).toEqual({});
        });

        it('should return empty object when holidays is not an array', () => {
            mockOrg.getHolidays.mockReturnValue('not an array');

            const result = HolidayUtils.getHolidaysInRange(1000, 2000, mockOrg);

            expect(result).toEqual({});
        });

        it('should handle individual holiday within range', () => {
            const holiday = {
                _id: 'h1',
                name: 'Independence Day',
                date: '2025-07-04',
                dateType: HOLIDAY_TYPES.INDIVIDUAL
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            // July 4, 2025 in Chicago timezone
            const july4 = moment.tz('2025-07-04', timezone).valueOf();
            const rangeStart = july4 - 86400000; // Day before
            const rangeEnd = july4 + 86400000; // Day after

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result['2025-07-04']).toEqual(holiday);
        });

        it('should exclude individual holiday outside range', () => {
            const holiday = {
                _id: 'h1',
                name: 'Independence Day',
                date: '2025-07-04',
                dateType: HOLIDAY_TYPES.INDIVIDUAL
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            // Range that doesn't include July 4th
            const rangeStart = moment.tz('2025-08-01', timezone).valueOf();
            const rangeEnd = moment.tz('2025-08-31', timezone).valueOf();

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result).toEqual({});
        });

        it('should handle range holiday completely within range', () => {
            const holiday = {
                _id: 'h1',
                name: 'Spring Break',
                startDate: '2025-03-10',
                endDate: '2025-03-14',
                dateType: HOLIDAY_TYPES.RANGE
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            const rangeStart = moment.tz('2025-03-01', timezone).valueOf();
            const rangeEnd = moment.tz('2025-03-31', timezone).valueOf();

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result['2025-03-10']).toEqual(holiday);
            expect(result['2025-03-11']).toEqual(holiday);
            expect(result['2025-03-12']).toEqual(holiday);
            expect(result['2025-03-13']).toEqual(holiday);
            expect(result['2025-03-14']).toEqual(holiday);
            expect(Object.keys(result)).toHaveLength(5);
        });

        it('should handle range holiday partially overlapping range', () => {
            const holiday = {
                _id: 'h1',
                name: 'Spring Break',
                startDate: '2025-03-10',
                endDate: '2025-03-20',
                dateType: HOLIDAY_TYPES.RANGE
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            // Range that only includes part of the holiday
            const rangeStart = moment.tz('2025-03-15', timezone).valueOf();
            const rangeEnd = moment.tz('2025-03-25', timezone).valueOf();

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result['2025-03-15']).toEqual(holiday);
            expect(result['2025-03-16']).toEqual(holiday);
            expect(result['2025-03-17']).toEqual(holiday);
            expect(result['2025-03-18']).toEqual(holiday);
            expect(result['2025-03-19']).toEqual(holiday);
            expect(result['2025-03-20']).toEqual(holiday);
            expect(result['2025-03-14']).toBeUndefined();
            expect(result['2025-03-21']).toBeUndefined();
        });

        it('should handle legacy holiday without dateType (defaults to individual)', () => {
            const holiday = {
                _id: 'h1',
                name: 'Legacy Holiday',
                date: '2025-05-01'
                // No dateType property
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            const may1 = moment.tz('2025-05-01', timezone).valueOf();
            const rangeStart = may1 - 86400000;
            const rangeEnd = may1 + 86400000;

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result['2025-05-01']).toEqual(holiday);
        });

        it('should handle multiple holidays of different types', () => {
            const holidays = [
                {
                    _id: 'h1',
                    name: 'Independence Day',
                    date: '2025-07-04',
                    dateType: HOLIDAY_TYPES.INDIVIDUAL
                },
                {
                    _id: 'h2',
                    name: 'Spring Break',
                    startDate: '2025-07-10',
                    endDate: '2025-07-12',
                    dateType: HOLIDAY_TYPES.RANGE
                }
            ];

            mockOrg.getHolidays.mockReturnValue(holidays);

            const rangeStart = moment.tz('2025-07-01', timezone).valueOf();
            const rangeEnd = moment.tz('2025-07-15', timezone).valueOf();

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result['2025-07-04']).toEqual(holidays[0]);
            expect(result['2025-07-10']).toEqual(holidays[1]);
            expect(result['2025-07-11']).toEqual(holidays[1]);
            expect(result['2025-07-12']).toEqual(holidays[1]);
        });

        it('should handle single day range holiday', () => {
            const holiday = {
                _id: 'h1',
                name: 'Single Day Holiday',
                startDate: '2025-06-01',
                endDate: '2025-06-01',
                dateType: HOLIDAY_TYPES.RANGE
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            const june1 = moment.tz('2025-06-01', timezone).valueOf();
            const rangeStart = june1 - 86400000;
            const rangeEnd = june1 + 86400000;

            const result = HolidayUtils.getHolidaysInRange(rangeStart, rangeEnd, mockOrg);

            expect(result['2025-06-01']).toEqual(holiday);
            expect(Object.keys(result)).toHaveLength(1);
        });

        it('should handle holidays at range boundaries', () => {
            const holiday = {
                _id: 'h1',
                name: 'Boundary Holiday',
                date: '2025-01-01',
                dateType: HOLIDAY_TYPES.INDIVIDUAL
            };

            mockOrg.getHolidays.mockReturnValue([holiday]);

            const jan1 = moment.tz('2025-01-01', timezone).valueOf();

            // Test inclusive boundaries
            const result1 = HolidayUtils.getHolidaysInRange(jan1, jan1, mockOrg);
            expect(result1['2025-01-01']).toEqual(holiday);

            // Test exclusive end boundary
            const result2 = HolidayUtils.getHolidaysInRange(jan1 + 1, jan1 + 86400000, mockOrg);
            expect(result2['2025-01-01']).toBeUndefined();
        });
    });

    describe('getPermittedScheduleTypes', () => {
        it('should return "None" for holiday without scheduleTypes property', () => {
            const holiday = {
                _id: 'h1',
                name: 'Legacy Holiday'
                // No scheduleTypes property
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('None');
        });

        it('should return "None" for holiday with null scheduleTypes', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: null
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('None');
        });

        it('should return "None" for holiday with undefined scheduleTypes', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: undefined
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('None');
        });

        it('should return "All" when scheduleTypes includes "All"', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['All']
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('All');
        });

        it('should return "All" when scheduleTypes includes "All" with other types', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['type1', 'All', 'type2']
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('All');
        });

        it('should return "None" when scheduleTypes includes "None"', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['None']
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('None');
        });

        it('should return "None" when scheduleTypes includes "None" with other types', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['type1', 'None', 'type2']
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toBe('None');
        });

        it('should return array of specific schedule types', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['morning', 'afternoon']
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toEqual(['morning', 'afternoon']);
        });

        it('should return empty array when scheduleTypes is empty', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: []
            };

            const result = HolidayUtils.getPermittedScheduleTypes(holiday);

            expect(result).toEqual([]);
        });
    });

    describe('isScheduleTypePermitted', () => {
        it('should return true when permitted types is "All"', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['All']
            };

            expect(HolidayUtils.isScheduleTypePermitted('morning', holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted('afternoon', holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted('', holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted(null, holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted(undefined, holiday)).toBe(true);
        });

        it('should return false when permitted types is "None"', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['None']
            };

            expect(HolidayUtils.isScheduleTypePermitted('morning', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted('afternoon', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted('', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted(null, holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted(undefined, holiday)).toBe(false);
        });

        it('should return false for legacy holiday without scheduleTypes (defaults to "None")', () => {
            const holiday = {
                _id: 'h1',
                name: 'Legacy Holiday'
                // No scheduleTypes property
            };

            expect(HolidayUtils.isScheduleTypePermitted('morning', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted('afternoon', holiday)).toBe(false);
        });

        it('should return true when schedule type is in permitted array', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['morning', 'evening']
            };

            expect(HolidayUtils.isScheduleTypePermitted('morning', holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted('evening', holiday)).toBe(true);
        });

        it('should return false when schedule type is not in permitted array', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['morning', 'evening']
            };

            expect(HolidayUtils.isScheduleTypePermitted('afternoon', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted('night', holiday)).toBe(false);
        });

        it('should handle empty string schedule type', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['', 'morning']
            };

            expect(HolidayUtils.isScheduleTypePermitted('', holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted(null, holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted(undefined, holiday)).toBe(true);
        });

        it('should handle null and undefined schedule type IDs', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['morning']
            };

            expect(HolidayUtils.isScheduleTypePermitted(null, holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted(undefined, holiday)).toBe(false);
        });

        it('should be case sensitive for schedule type matching', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: ['Morning']
            };

            expect(HolidayUtils.isScheduleTypePermitted('Morning', holiday)).toBe(true);
            expect(HolidayUtils.isScheduleTypePermitted('morning', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted('MORNING', holiday)).toBe(false);
        });

        it('should handle empty scheduleTypes array', () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday',
                scheduleTypes: []
            };

            expect(HolidayUtils.isScheduleTypePermitted('morning', holiday)).toBe(false);
            expect(HolidayUtils.isScheduleTypePermitted('', holiday)).toBe(false);
        });
    });
});