// Mock FlowRouter before importing anything
jest.mock('meteor/ostrio:flow-router-extra', () => ({
    FlowRouter: {
        current: jest.fn(),
        getQueryParam: jest.fn(),
        setQueryParams: jest.fn(),
    }
}));

// Mock DEFAULT_SHOW_SECTIONS
jest.mock('../../../../lib/calendar/calendarConstants', () => ({
    DEFAULT_SHOW_SECTIONS: ['activities', 'announcements', 'food', 'reservations']
}));

// Now import the modules
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { DEFAULT_SHOW_SECTIONS } from '../../../../lib/calendar/calendarConstants';
import { getShowSections, formatGroups, matchesQuery } from '../../../../lib/calendar/calendarUtils';

describe('Calendar Utils', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    describe('getShowSections', () => {
        it('should return showOnly query parameter when present', () => {
            FlowRouter.getQueryParam.mockReturnValue('activities,food');

            const result = getShowSections({ show: 'reservations,announcements' });

            expect(result).toBe('activities,food');
            expect(FlowRouter.getQueryParam).toHaveBeenCalledWith('showOnly');
        });

        it('should return split data.show when showOnly is not present', () => {
            FlowRouter.getQueryParam.mockReturnValue(null);

            const result = getShowSections({ show: 'reservations,announcements' });

            expect(result).toEqual(['reservations', 'announcements']);
        });

        it('should return DEFAULT_SHOW_SECTIONS when data.show is not present', () => {
            FlowRouter.getQueryParam.mockReturnValue(null);

            const result = getShowSections({});

            expect(result).toBe(DEFAULT_SHOW_SECTIONS);
        });

        it('should return DEFAULT_SHOW_SECTIONS when data is null/undefined', () => {
            FlowRouter.getQueryParam.mockReturnValue(null);

            expect(getShowSections(null)).toBe(DEFAULT_SHOW_SECTIONS);
            expect(getShowSections(undefined)).toBe(DEFAULT_SHOW_SECTIONS);
        });

        it('should handle empty string in data.show', () => {
            FlowRouter.getQueryParam.mockReturnValue(null);

            const result = getShowSections({ show: '' });

            expect(result).toEqual(['']);
        });
    });

    describe('formatGroups', () => {
        it('should format multiple groups with commas', () => {
            const groups = ['Group A', 'Group B', 'Group C'];
            const result = formatGroups(groups);

            expect(result).toBe('Group A, Group B, Group C');
        });

        it('should handle single group without comma', () => {
            const groups = ['Single Group'];
            const result = formatGroups(groups);

            expect(result).toBe('Single Group');
        });

        it('should return empty string for empty array', () => {
            const groups = [];
            const result = formatGroups(groups);

            expect(result).toBe('');
        });

        it('should handle groups with special characters', () => {
            const groups = ['Group & Co.', 'Group #2', 'Group (Special)'];
            const result = formatGroups(groups);

            expect(result).toBe('Group & Co., Group #2, Group (Special)');
        });

        it('should handle numeric and mixed type groups', () => {
            const groups = ['Group 1', '2nd Group', 'Final Group'];
            const result = formatGroups(groups);

            expect(result).toBe('Group 1, 2nd Group, Final Group');
        });
    });

    describe('matchesQuery', () => {
        describe('simple equality matching', () => {
            it('should match when all fields are equal', () => {
                const doc = { name: 'John', age: 25, city: 'New York' };
                const query = { name: 'John', age: 25 };

                expect(matchesQuery(doc, query)).toBe(true);
            });

            it('should not match when any field is not equal', () => {
                const doc = { name: 'John', age: 25 };
                const query = { name: 'John', age: 30 };

                expect(matchesQuery(doc, query)).toBe(false);
            });

            it('should handle null and undefined values', () => {
                const doc = { name: null, age: undefined };

                expect(matchesQuery(doc, { name: null })).toBe(true);
                expect(matchesQuery(doc, { age: undefined })).toBe(true);
                expect(matchesQuery(doc, { name: 'John' })).toBe(false);
            });
        });

        describe('$gte operator', () => {
            it('should match when value is greater than or equal', () => {
                const doc = { age: 25, score: 90 };

                expect(matchesQuery(doc, { age: { $gte: 25 } })).toBe(true);
                expect(matchesQuery(doc, { age: { $gte: 20 } })).toBe(true);
                expect(matchesQuery(doc, { score: { $gte: 90 } })).toBe(true);
            });

            it('should not match when value is less than', () => {
                const doc = { age: 25 };

                expect(matchesQuery(doc, { age: { $gte: 30 } })).toBe(false);
            });
        });

        describe('$lte operator', () => {
            it('should match when value is less than or equal', () => {
                const doc = { age: 25, score: 90 };

                expect(matchesQuery(doc, { age: { $lte: 25 } })).toBe(true);
                expect(matchesQuery(doc, { age: { $lte: 30 } })).toBe(true);
                expect(matchesQuery(doc, { score: { $lte: 90 } })).toBe(true);
            });

            it('should not match when value is greater than', () => {
                const doc = { age: 25 };

                expect(matchesQuery(doc, { age: { $lte: 20 } })).toBe(false);
            });
        });

        describe('combined $gte and $lte operators', () => {
            it('should match when value is within range', () => {
                const doc = { age: 25, score: 85 };

                expect(matchesQuery(doc, { age: { $gte: 20, $lte: 30 } })).toBe(true);
                expect(matchesQuery(doc, { score: { $gte: 80, $lte: 90 } })).toBe(true);
            });

            it('should not match when value is outside range', () => {
                const doc = { age: 35 };

                expect(matchesQuery(doc, { age: { $gte: 20, $lte: 30 } })).toBe(false);
            });
        });

        describe('$in operator', () => {
            it('should match when non-array value is in the array', () => {
                const doc = { category: 'electronics', status: 'active' };

                expect(matchesQuery(doc, { category: { $in: ['electronics', 'books'] } })).toBe(true);
                expect(matchesQuery(doc, { status: { $in: ['active', 'inactive'] } })).toBe(true);
            });

            it('should not match when non-array value is not in the array', () => {
                const doc = { category: 'clothing' };

                expect(matchesQuery(doc, { category: { $in: ['electronics', 'books'] } })).toBe(false);
            });

            it('should match when array field has overlap with $in array', () => {
                const doc = { tags: ['javascript', 'node', 'react'] };

                expect(matchesQuery(doc, { tags: { $in: ['react', 'vue'] } })).toBe(true);
                expect(matchesQuery(doc, { tags: { $in: ['python', 'javascript'] } })).toBe(true);
            });

            it('should not match when array field has no overlap with $in array', () => {
                const doc = { tags: ['javascript', 'node'] };

                expect(matchesQuery(doc, { tags: { $in: ['python', 'java'] } })).toBe(false);
            });
        });

        describe('$or operator', () => {
            it('should match when at least one condition is true', () => {
                const doc = { name: 'John', age: 25, city: 'Boston' };

                expect(matchesQuery(doc, { $or: [{ name: 'John' }, { age: 30 }] })).toBe(true);
                expect(matchesQuery(doc, { $or: [{ name: 'Jane' }, { age: 25 }] })).toBe(true);
                expect(matchesQuery(doc, { $or: [{ city: 'Boston' }, { age: 30 }] })).toBe(true);
            });

            it('should not match when no conditions are true', () => {
                const doc = { name: 'John', age: 25 };

                expect(matchesQuery(doc, { $or: [{ name: 'Jane' }, { age: 30 }] })).toBe(false);
            });

            it('should work with complex conditions in $or', () => {
                const doc = { age: 25, score: 85 };

                expect(matchesQuery(doc, {
                    $or: [
                        { age: { $gte: 30 } },
                        { score: { $gte: 80 } }
                    ]
                })).toBe(true);
            });
        });

        describe('$elemMatch operator', () => {
            it('should match when array contains element matching all conditions', () => {
                const doc = { scores: [75, 88, 92, 65] };

                expect(matchesQuery(doc, {
                    scores: { $elemMatch: { $gte: 80, $lte: 90 } }
                })).toBe(true);
            });

            it('should not match when no array element matches all conditions', () => {
                const doc = { scores: [75, 95, 65] };

                expect(matchesQuery(doc, {
                    scores: { $elemMatch: { $gte: 80, $lte: 90 } }
                })).toBe(false);
            });

            it('should not match when field is not an array', () => {
                const doc = { scores: 85 };

                expect(matchesQuery(doc, {
                    scores: { $elemMatch: { $gte: 80, $lte: 90 } }
                })).toBe(false);
            });

            it('should handle multiple conditions in $elemMatch', () => {
                const doc = { items: [10, 25, 30, 45] };

                expect(matchesQuery(doc, {
                    items: { $elemMatch: { $gte: 20, $lte: 30 } }
                })).toBe(true);
            });
        });

        describe('complex queries', () => {
            it('should handle combination of operators', () => {
                const doc = {
                    name: 'John',
                    age: 25,
                    tags: ['developer', 'javascript'],
                    scores: [80, 90, 75]
                };

                const query = {
                    age: { $gte: 20, $lte: 30 },
                    tags: { $in: ['javascript', 'python'] },
                    scores: { $elemMatch: { $gte: 85 } }
                };

                expect(matchesQuery(doc, query)).toBe(true);
            });

            it('should handle $or with multiple complex conditions', () => {
                const doc = {
                    category: 'electronics',
                    price: 500,
                    ratings: [4, 5, 3]
                };

                const query = {
                    $or: [
                        {
                            category: 'electronics',
                            price: { $lte: 600 }
                        },
                        {
                            ratings: { $elemMatch: { $gte: 4 } }
                        }
                    ]
                };

                expect(matchesQuery(doc, query)).toBe(true);
            });
        });

        describe('edge cases', () => {
            it('should handle empty query object', () => {
                const doc = { name: 'John', age: 25 };

                expect(matchesQuery(doc, {})).toBe(true);
            });

            it('should handle missing fields in document', () => {
                const doc = { name: 'John' };

                expect(matchesQuery(doc, { age: 25 })).toBe(false);
                expect(matchesQuery(doc, { age: undefined })).toBe(false);
            });

            it('should handle array and object edge cases', () => {
                const doc = {
                    emptyArray: [],
                    emptyObject: {},
                    nullValue: null
                };

                expect(matchesQuery(doc, { emptyArray: [] })).toBe(true);
                expect(matchesQuery(doc, { emptyObject: {} })).toBe(true);
                expect(matchesQuery(doc, { nullValue: null })).toBe(true);
            });
        });
    });
});