// Mock FlowRouter before importing anything
jest.mock('meteor/ostrio:flow-router-extra', () => ({
    FlowRouter: {
        current: jest.fn(),
        getQueryParam: jest.fn(),
        setQueryParams: jest.fn(),
    }
}));

import { CalendarService } from '../../../../../client/app/calendar/calendarService';
import { Meteor } from 'meteor/meteor';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { Curriculum } from '../../../../../lib/collections/curriculum';
import { CurriculumTheme } from '../../../../../lib/collections/curriculumThemes';
import { Orgs } from '../../../../../lib/collections/orgs';
import { USER_TYPES } from '../../../../../lib/constants/profileConstants';
import { Announcements } from '../../../../../lib/collections/announcements';
import { Foods } from '../../../../../lib/collections/food';
import { Reservations } from '../../../../../lib/collections/reservations';
import { Groups } from '../../../../../lib/collections/groups';
import moment from 'moment-timezone';
import { CALENDAR_VIEWS } from '../../../../../lib/calendar/calendarConstants';

// Mock moment-timezone
jest.mock('moment-timezone', () => {
    const actualMoment = jest.requireActual('moment-timezone');
    const mockMoment = jest.fn((input, timezone) => {
        if (timezone) {
            return actualMoment.tz(input, timezone);
        }
        return actualMoment(input);
    });

    // Copy all methods from actual moment
    Object.setPrototypeOf(mockMoment, actualMoment);
    Object.assign(mockMoment, actualMoment);

    mockMoment.tz = actualMoment.tz;

    return mockMoment;
});

jest.mock('../../../../../lib/collections/orgs');
jest.mock('../../../../../lib/collections/curriculumThemes');
jest.mock('../../../../../lib/collections/curriculum');
jest.mock('../../../../../lib/collections/announcements');
jest.mock('../../../../../lib/collections/food');
jest.mock('../../../../../lib/collections/reservations');
jest.mock('../../../../../lib/collections/groups');
jest.mock('../../../../../client/services/peopleMeteorService', () => ({
    getPeopleData: jest.fn()
}));
jest.mock('../../../../../lib/calendar/calendarUtils', () => ({
    ...jest.requireActual('../../../../../lib/calendar/calendarUtils'),
    matchesQuery: jest.fn(() => true)
}));

jest.mock('meteor/reactive-var', () => ({
    ReactiveVar: jest.fn().mockImplementation(initial => {
        let value = initial;
        return {
            get: () => value,
            set: (newVal) => value = newVal,
        };
    }),
}));

describe('CalendarService', () => {
    describe('generateBaseQuery', () => {
        let service;

        const baseRangeStart = 1000;
        const baseRangeEnd = 2000;

        beforeEach(() => {
            service = new CalendarService({
                timezone: 'America/Chicago',
                showSections: ['activities'],
                filterOptions: {},
                currentPerson: null,
            });
        });

        it('returns basic query for unknown user type', () => {
            service.selectedGroup.set('group1');

            const result = service.generateBaseQuery(baseRangeStart, baseRangeEnd, null);
            expect(result).toEqual({
                $or: [
                    { selectedGroups: [] },
                    { selectedGroups: { $in: ['group1'] } }
                ],
                scheduledDate: {
                    $gte: baseRangeStart,
                    $lte: baseRangeEnd,
                }
            });
        });

        it('builds query for family user with inherited relationships', () => {
            const mockPerson = {
                type: USER_TYPES.FAMILY,
                findInheritedRelationships: () => [
                    { targetId: 'child1' },
                    { targetId: 'child2' },
                ]
            };

            service.allReservationsPeople.set([
                { _id: 'child1', defaultGroupId: 'g1' },
                { _id: 'child2', defaultGroupId: 'g2' },
            ]);

            const result = service.generateBaseQuery(baseRangeStart, baseRangeEnd, mockPerson);
            expect(result).toMatchObject({
                scheduledDate: { $gte: baseRangeStart, $lte: baseRangeEnd },
                $or: [
                    { selectedGroups: [] },
                    { selectedGroups: { $in: ['g1', 'g2'] } }
                ]
            });
        });

        it('builds query for staff user with selected group', () => {
            const mockPerson = {
                type: USER_TYPES.STAFF,
                checkInGroupId: 'checkin-group',
                defaultGroupId: 'default-group'
            };

            service.selectedGroup.set('group123');

            const result = service.generateBaseQuery(baseRangeStart, baseRangeEnd, mockPerson);
            expect(result).toMatchObject({
                scheduledDate: { $gte: baseRangeStart, $lte: baseRangeEnd },
                $or: [
                    { selectedGroups: [] },
                    { selectedGroups: { $in: ['group123'] } }
                ]
            });
        });

        it('falls back to checkInGroupId if no selectedGroup is set', () => {
            const mockPerson = {
                type: USER_TYPES.STAFF,
                checkInGroupId: 'checkin-fallback',
                defaultGroupId: 'default-group'
            };

            service.selectedGroup.set("");

            const result = service.generateBaseQuery(baseRangeStart, baseRangeEnd, mockPerson);
            expect(result).toMatchObject({
                $or: [
                    { selectedGroups: [] },
                    { selectedGroups: { $in: ['checkin-fallback'] } }
                ]
            });
        });

        it('uses defaultGroupId if no selectedGroup or checkInGroupId', () => {
            const mockPerson = {
                type: USER_TYPES.STAFF,
                defaultGroupId: 'fallback-default'
            };

            service.selectedGroup.set("");

            const result = service.generateBaseQuery(baseRangeStart, baseRangeEnd, mockPerson);
            expect(result).toMatchObject({
                $or: [
                    { selectedGroups: [] },
                    { selectedGroups: { $in: ['fallback-default'] } }
                ]
            });
        });

        it('builds query for admin user using selectedGroup', () => {
            const mockPerson = {
                type: USER_TYPES.ADMIN
            };

            service.selectedGroup.set("admin-group");

            const result = service.generateBaseQuery(baseRangeStart, baseRangeEnd, mockPerson);
            expect(result).toMatchObject({
                $or: [
                    { selectedGroups: [] },
                    { selectedGroups: { $in: ['admin-group'] } }
                ]
            });
        });
    });

    describe('getCurriculumEvents', () => {
        let service;
        beforeEach(() => {
            Curriculum.mockImplementation(data => data);
            Orgs.current.mockReturnValue({});

            service = new CalendarService({
                timezone: 'America/Chicago',
                showSections: [],
                filterOptions: {},
                currentPerson: { type: 'staff' },
                data: {}
            });

            service.selectedGroup = new ReactiveVar('group1');
            service.allReservationsPeople = new ReactiveVar([]);
            service.curriculumData = new ReactiveVar([
                {
                    _id: 'c1',
                    scheduledDate: 1234,
                    selectedTypes: ['TypeA'],
                    headline: 'Math Time',
                    message: 'Do math!',
                    teacherNotes: 'Be patient',
                    sendToAll: true,
                    curriculumThemeId: null,
                    selectedGroups: [],
                    findRecipientGroups: () => [],
                }
            ]);
        });

        it('should return curriculum events without themes', () => {
            const results = service.getCurriculumEvents(1000, 2000);
            expect(results).toHaveLength(1);
            expect(results[0]).toMatchObject({
                title: 'Math Time',
                subheader: 'TypeA',
                eventType: 'activities'
            });
        });
    });

    describe('getCurriculumThemeEvents', () => {
        let service;

        beforeEach(() => {
            CurriculumTheme.mockImplementation(data => data);
            Curriculum.mockImplementation(data => data);

            service = new CalendarService({
                timezone: 'America/Chicago',
                showSections: [],
                filterOptions: {},
                currentPerson: { type: 'staff' },
                data: {}
            });

            service.curriculumThemesData = new ReactiveVar([
                {
                    _id: 't1',
                    name: 'Colors',
                    description: 'Learning about colors',
                    selectedDays: [1234],
                    findRecipientGroups: () => [{ name: 'Red Group' }]
                }
            ]);

            service.curriculumData = new ReactiveVar([
                {
                    _id: 'c1',
                    curriculumThemeId: 't1',
                    scheduledDate: 1234,
                    selectedTypes: ['TypeA'],
                    headline: 'Red vs Blue',
                    message: 'Paint time',
                    teacherNotes: '',
                    findRecipientGroups: () => [],
                }
            ]);
        });

        it('should return both theme headers and nested curriculum events', () => {
            const results = service.getCurriculumThemeEvents(1000, 2000);
            expect(results).toHaveLength(2);

            const themeHeader = results.find(e => e.isCurriculumTheme);
            const nestedEvent = results.find(e => e.hasCurriculumThemeId === 't1');

            expect(themeHeader.title).toContain('Theme: Colors');
            expect(nestedEvent.title).toBe('Red vs Blue');
        });

        it('should return curriculum theme header event on matching day', () => {
            service.curriculumThemesData = new ReactiveVar([
                {
                    _id: 'theme1',
                    name: 'Science Week',
                    description: 'Explore nature and experiments',
                    selectedDays: [1749013200000], // 2025-06-04T00:00:00 Central
                    selectedGroups: [],
                    findRecipientGroups: () => [{ name: 'Group A' }]
                }
            ]);

            const results = service.getCurriculumThemeEvents(1748926800000, 1749186000000);
            expect(results).toHaveLength(1);
            expect(results[0]).toMatchObject({
                title: 'Theme: Science Week',
                eventType: 'activities',
                eventSubType: 'theme',
                start: 1749013200000
            });
        });

        it('should return curriculum child event for theme day match', () => {
            service.curriculumThemesData = new ReactiveVar([
                {
                    _id: 'theme1',
                    name: 'Science Week',
                    description: 'Explore nature and experiments',
                    selectedDays: [1749013200000], // 2025-06-04T00:00:00
                    selectedGroups: [],
                    findRecipientGroups: () => [{ name: 'Group A' }]
                }
            ]);

            service.curriculumData = new ReactiveVar([
                {
                    _id: 'curric1',
                    curriculumThemeId: 'theme1',
                    scheduledDate: 1749013200000,
                    headline: 'Volcano Building',
                    message: 'Make a baking soda volcano!',
                    selectedTypes: ['TypeX'],
                    mediaFiles: [],
                    sendToAll: true
                }
            ]);

            const results = service.getCurriculumThemeEvents(1748926800000, 1749186000000);

            const childEvent = results.find(e => e.title === 'Volcano Building');
            expect(childEvent).toBeDefined();
            expect(childEvent).toMatchObject({
                title: 'Volcano Building',
                eventType: 'activities',
                start: 1749013200000
            });
        });
    });

    describe('getAnnouncementEvents', () => {
        let service;

        beforeEach(() => {
            service = new CalendarService({
                timezone: 'America/Chicago',
                showSections: ['announcements'],
                filterOptions: {},
                currentPerson: {},
                data: {}
            });

            service.selectedGroup = new ReactiveVar('groupA');
        });

        it('should return announcements that match selected group', () => {
            const mockAnnouncements = [
                {
                    _id: 'a1',
                    headline: 'Field Day',
                    message: 'Bring water and sunscreen!',
                    scheduledDate: 1749013200000,
                    scheduledEndDate: 1749099600000,
                    mediaFiles: [],
                    sendToAll: () => false,
                    findRecipientGroups: () => [{ name: 'Group A' }],
                    selectedGroups: ['groupA']
                },
                {
                    _id: 'a2',
                    headline: 'Closed Monday',
                    message: 'Holiday closure.',
                    scheduledDate: 1749013200000,
                    scheduledEndDate: 1749099600000,
                    mediaFiles: [],
                    sendToAll: () => true,
                    findRecipientGroups: () => [],
                    selectedGroups: []
                }
            ];

            Announcements.find.mockReturnValue(mockAnnouncements);

            const results = service.getAnnouncementEvents();
            expect(results).toHaveLength(2);
            expect(results[0]).toMatchObject({
                title: 'Field Day',
                eventType: 'announcements'
            });
            expect(results[1]).toMatchObject({
                title: 'Closed Monday',
                eventType: 'announcements'
            });
        });

        it('should exclude announcements that don’t match selected group', () => {
            const mockAnnouncements = [
                {
                    _id: 'a3',
                    headline: 'Other Group Event',
                    message: 'Not for groupA',
                    scheduledDate: 1749013200000,
                    scheduledEndDate: 1749099600000,
                    mediaFiles: [],
                    sendToAll: () => false,
                    findRecipientGroups: () => [{ name: 'Group B' }],
                    selectedGroups: ['groupB']
                }
            ];

            // Simulate the DB filter logic using the mock document
            Announcements.find.mockImplementation((query) => {
                return mockAnnouncements.filter(a => {
                    if (!query?.$or) return true;

                    return query.$or.some(condition => {
                        if (condition.selectedGroups?.$size === 0) {
                            return a.selectedGroups.length === 0;
                        }

                        if (condition.selectedGroups?.$in) {
                            return a.selectedGroups.some(group => condition.selectedGroups.$in.includes(group));
                        }

                        return false;
                    });
                });
            });

            const results = service.getAnnouncementEvents();
            expect(results).toHaveLength(0);
        });

        it('should return all announcements when no selected group is set', () => {
            service.selectedGroup = new ReactiveVar('');

            const mockAnnouncements = [
                {
                    _id: 'a4',
                    headline: 'General Event',
                    message: 'Open to all',
                    scheduledDate: 1749013200000,
                    scheduledEndDate: 1749099600000,
                    mediaFiles: [],
                    sendToAll: () => true,
                    findRecipientGroups: () => [],
                    selectedGroups: []
                }
            ];

            Announcements.find.mockReturnValue(mockAnnouncements);

            const results = service.getAnnouncementEvents();
            expect(results).toHaveLength(1);
            expect(results[0].title).toBe('General Event');
        });
    });

    describe('getFoodEvents', () => {
        let service;

        beforeEach(() => {
            service = new CalendarService({
                timezone: 'America/Chicago',
                showSections: ['food'],
                filterOptions: {},
                currentPerson: {},
                data: {}
            });

            service.selectedGroup = new ReactiveVar('groupA');
            service.allReservationsPeople = new ReactiveVar([]);
        });

        it('should return food events within the range and matching group filter', () => {
            const mockFood = {
                _id: 'f1',
                originalId: 'originalF1',
                scheduledDate: 1748995200000,
                meal: 'Lunch',
                mediaFiles: [],
                processedDescription: () => 'Pizza Day!',
                sendToAll: () => false,
                findRecipientGroups: () => [{ name: 'Group A' }]
            };

            Foods.findWithRecurrence.mockReturnValue([mockFood]);

            const results = service.getFoodEvents(1748995200000, 1749081600000);
            expect(results).toHaveLength(1);
            expect(results[0]).toMatchObject({
                title: 'Lunch: Pizza Day!',
                eventType: 'food',
                start: 1748995200000
            });
        });

        it('should return event with null eventTargets if sendToAll is true', () => {
            const mockFood = {
                _id: 'f2',
                scheduledDate: 1748995200000,
                meal: 'Snack',
                mediaFiles: [],
                processedDescription: () => 'Fruit',
                sendToAll: () => true,
                findRecipientGroups: () => []
            };

            Foods.findWithRecurrence.mockReturnValue([mockFood]);

            const results = service.getFoodEvents(1748995200000, 1749081600000);
            expect(results[0].eventTargets).toBeNull();
        });
    })

    describe('getReservationEvents', () => {
        let service;

        beforeEach(() => {
            // Reset module state
            jest.clearAllMocks();

            // Fresh instance of the calendar service
            service = new CalendarService({
                timezone: 'America/Chicago',
                showSections: ['reservations'],
                filterOptions: {},
                currentPerson: {},
                data: { show: '' }
            });

            // Mock current person (admin for default case)
            service.currentPerson = { type: 'admin' };

            // Reactive selected group mock
            service.selectedGroup = {
                get: jest.fn(() => null)
            };

            // Mock people for reservation resolution
            service.allReservationsPeople = {
                get: jest.fn(() => [
                    {
                        _id: 'p1',
                        firstName: 'Jane',
                        lastName: 'Doe',
                        checkInOrder: 1,
                        checkedIn: true,
                        checkedInOutTime: new Date().setHours(0, 0, 0, 0),
                        findDefaultGroup: () => ({ _id: 'g1', name: 'Group A' })
                    }
                ])
            };

            // Mock schedule types
            service.currentOrg = {
                hasCustomization: jest.fn(() => false),
                getScheduleTypes: jest.fn(() => [
                    { _id: 'st1', type: 'Morning', sortStart: 1 }
                ]),
                getHolidays: jest.fn(() => []),
            };

            // Default stub for Groups.findOne
            Groups.findOne = jest.fn((id) => {
                const groups = {
                    groupA: { _id: 'groupA', name: 'Group A', capacity: 10, ratio: 5, sortOrder: 1 },
                    groupB: { _id: 'groupB', name: 'Group B', capacity: 5, ratio: 1, sortOrder: 2 }
                };
                return groups[id] || { _id: id, name: 'Unknown', capacity: 0, ratio: 1, sortOrder: 99 };
            });

            // Mock Orgs.current()
            Orgs.current = jest.fn(() => ({
                hasCustomization: jest.fn(() => false),
                orgReservationCapacity: null,
                getScheduleTypes: jest.fn(() => [])
            }));

            // Default mock for Reservations.findWithRecurrence
            Reservations.findWithRecurrence = jest.fn(() => []);
        });

        it('should return reservation events within the range', () => {
            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r1',
                    selectedPerson: 'p1',
                    scheduledDate: 1749013200000,
                    scheduleType: 'scheduleType1',
                    findRecipientGroups: () => [{ name: 'Group A' }]
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p1',
                    firstName: 'John',
                    lastName: 'Doe',
                    findDefaultGroup: () => ({ _id: 'groupA', name: 'Group A', capacity: 5, ratio: 1 })
                }
            ]);

            service.currentPerson = { type: USER_TYPES.ADMIN };
            service.selectedGroup = new ReactiveVar(null);

            const results = service.getReservationEvents(1749013200000, 1749099600000, false, '');
            expect(results).toHaveLength(1);
            expect(results[0]).toMatchObject({
                start: 1749013200000,
                eventType: 'reservationSlot'
            });
        });

        it('should return empty array when there are no reservations', () => {
            Reservations.findWithRecurrence.mockReturnValue([]);
            const results = service.getReservationEvents(1749013200000, 1749099600000, false, '');
            expect(results).toEqual([]);
        });

        it('should create a reservationCapacity header event for today', () => {
            const timezone = 'America/Chicago';
            const todayStart = moment.tz(timezone).startOf('day').valueOf();
            const todayEnd = moment.tz(timezone).endOf('day').valueOf();
            service.timezone = timezone;

            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r1',
                    scheduledDate: todayStart,
                    scheduleType: 'st1',
                    selectedPerson: 'p1'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p1',
                    firstName: 'Jane',
                    lastName: 'Doe',
                    checkedIn: true,
                    checkedInOutTime: todayStart,
                    findDefaultGroup: () => ({ _id: 'g1', name: 'Group A', capacity: 10, ratio: 5 }),
                    checkInOrder: 2
                }
            ]);

            const results = service.getReservationEvents(todayStart, todayEnd, true, '');
            const header = results.find(e => e.eventType === 'reservationCapacity');
            expect(header).toBeTruthy();
            expect(header.title).toMatch(/Check-ins: 1\/1/);
        });

        it('should generate group summary headers when showReservationsByGroup is true', () => {
            const scheduledDate = 1749013200000;

            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r2',
                    scheduledDate,
                    scheduleType: 'st1',
                    selectedPerson: 'p2'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p2',
                    firstName: 'Jake',
                    lastName: 'Smith',
                    findDefaultGroup: () => ({ _id: 'g2', name: 'Group B', capacity: 5, ratio: 2 })
                }
            ]);

            const results = service.getReservationEvents(scheduledDate, scheduledDate + 86400000, true, '');
            const groupHeader = results.find(e => e.eventType === 'reservationCapacityByGroup');
            expect(groupHeader).toBeTruthy();
            expect(groupHeader.title).toBe('Group B');
        });

        it('should only show group header matching selectedGroup', () => {
            service.selectedGroup = new ReactiveVar('groupB');

            const scheduledDate = 1749013200000;

            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r3',
                    scheduledDate,
                    scheduleType: 'st1',
                    selectedPerson: 'p3',
                    groupId: 'groupB'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p3',
                    firstName: 'Ben',
                    lastName: 'Jones',
                    findDefaultGroup: () => ({ _id: 'groupB', name: 'Group B', capacity: 5, ratio: 1 })
                }
            ]);

            const results = service.getReservationEvents(scheduledDate, scheduledDate + 86400000, true, '');

            expect(results.some(e => e.eventTargets?.includes('Group B'))).toBe(true);
            expect(results.some(e => e.eventTargets?.includes('Group A'))).toBe(false);
        });

        it('should nest reservations under schedule types when sorted by schedule-type', () => {
            service.currentOrg.getScheduleTypes = () => [
                { _id: 'st1', type: 'AM', startTime: '8:00', endTime: '11:00', sortStart: 1 }
            ];

            const scheduledDate = 1749013200000;

            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r4',
                    scheduledDate,
                    scheduleType: 'st1',
                    selectedPerson: 'p4'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p4',
                    firstName: 'Abby',
                    lastName: 'Taylor',
                    findDefaultGroup: () => ({ _id: 'g4', name: 'Group D', capacity: 4, ratio: 2 })
                }
            ]);

            const results = service.getReservationEvents(scheduledDate, scheduledDate + 86400000, true, 'schedule-type');
            expect(results.some(e => e.eventType === 'reservationCapacityByScheduleType')).toBe(true);
            expect(results.some(e => e.additionalStyle === 'nested-double')).toBe(true);
        });

        it('should add nested style when reservationsSort is "group"', () => {
            const scheduledDate = 1749013200000;

            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r5',
                    scheduledDate,
                    scheduleType: 'st1',
                    selectedPerson: 'p5'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p5',
                    firstName: 'Olivia',
                    lastName: 'King',
                    findDefaultGroup: () => ({ _id: 'g5', name: 'Group E', capacity: 3, ratio: 1 })
                }
            ]);

            const results = service.getReservationEvents(scheduledDate, scheduledDate + 86400000, true, 'group');
            expect(results.some(e => e.additionalStyle === 'nested')).toBe(true);
        });

        it('should return reservation events directly when no sort applied', () => {
            const scheduledDate = 1749013200000;

            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r6',
                    scheduledDate,
                    scheduleType: 'st1',
                    selectedPerson: 'p6'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p6',
                    firstName: 'Noah',
                    lastName: 'Reed',
                    findDefaultGroup: () => ({ _id: 'g6', name: 'Group F', capacity: 2, ratio: 1 })
                }
            ]);

            const results = service.getReservationEvents(scheduledDate, scheduledDate + 86400000, true, '');
            expect(results.some(e => e.eventType === 'reservationSlot')).toBe(true);
        });

        it('should increment check-in count and show check-in order in title', () => {
            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r-checkin',
                    scheduledDate: new Date().getTime(), // Today
                    scheduleType: 'st1',
                    selectedPerson: 'p1'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p1',
                    firstName: 'Ada',
                    lastName: 'Lovelace',
                    checkedIn: true,
                    checkedInOutTime: new Date().getTime(), // today
                    checkInOrder: 3,
                    findDefaultGroup: () => ({ _id: 'group1', name: 'Group One', capacity: 10, ratio: 5 })
                }
            ]);

            const results = service.getReservationEvents(1749099600000, 1749186000000, true, '');
            const title = results.find(e => e.eventType === 'reservationSlot')?.title;
            expect(title).toContain('#3');
        });

        it('should still create event if default group is missing', () => {
            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r-nogroup',
                    scheduledDate: 1749099600000,
                    scheduleType: 'st1',
                    selectedPerson: 'p2'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p2',
                    firstName: 'Marie',
                    lastName: 'Curie',
                    checkedIn: false,
                    findDefaultGroup: () => null
                }
            ]);

            const results = service.getReservationEvents(1749099600000, 1749186000000, false, '');
            expect(results.length).toBeGreaterThan(0);
            expect(results.find(e => e.eventItemId === 'r-nogroup')).toBeDefined();
        });

        it('should nest reservation events under group headers when sort is "group"', () => {
            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r-nest',
                    scheduledDate: 1749099600000,
                    scheduleType: 'st1',
                    selectedPerson: 'p3',
                    groupId: 'groupX'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p3',
                    firstName: 'Alan',
                    lastName: 'Turing',
                    findDefaultGroup: () => ({ _id: 'groupX', name: 'Group X', capacity: 10, ratio: 5 })
                }
            ]);

            const results = service.getReservationEvents(1749099600000, 1749186000000, true, 'group');
            expect(results.some(e => e.additionalStyle === 'nested')).toBe(true);
        });

        it('should group reservation events by schedule type when sort is "schedule-type"', () => {
            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r-sched',
                    scheduledDate: 1749099600000,
                    scheduleType: 'stSpecial',
                    selectedPerson: 'p4',
                    groupId: 'groupY'
                }
            ]);

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'p4',
                    firstName: 'Grace',
                    lastName: 'Hopper',
                    findDefaultGroup: () => ({ _id: 'groupY', name: 'Group Y', capacity: 10, ratio: 2 })
                }
            ]);

            // Define custom schedule types
            service.currentOrg.getScheduleTypes = () => [
                { _id: 'stSpecial', type: 'Special', sortStart: 1 }
            ];

            const results = service.getReservationEvents(1749099600000, 1749186000000, true, 'schedule-type');

            const hasScheduleHeader = results.some(e => e.eventType === 'reservationCapacityByScheduleType');
            const hasNestedDouble = results.some(e => e.additionalStyle === 'nested-double');
            expect(hasScheduleHeader).toBe(true);
            expect(hasNestedDouble).toBe(true);
        });

        it('should sort group headers by sortOrder and name', () => {
            Reservations.findWithRecurrence.mockReturnValue([
                {
                    _id: 'r-a',
                    scheduledDate: 1749099600000,
                    scheduleType: 'st1',
                    selectedPerson: 'pA',
                    groupId: 'groupA'
                },
                {
                    _id: 'r-b',
                    scheduledDate: 1749099600000,
                    scheduleType: 'st1',
                    selectedPerson: 'pB',
                    groupId: 'groupB'
                }
            ]);

            // Stub out group details by groupId with different sortOrder and name
            Groups.findOne = jest.fn((id) => {
                if (id === 'groupA') {
                    return { _id: 'groupA', name: 'Alpha Group', capacity: 10, ratio: 2, sortOrder: 2 };
                }
                if (id === 'groupB') {
                    return { _id: 'groupB', name: 'Beta Group', capacity: 10, ratio: 2, sortOrder: 1 };
                }
            });

            service.allReservationsPeople = new ReactiveVar([
                {
                    _id: 'pA',
                    firstName: 'Alice',
                    lastName: 'Wonder',
                    findDefaultGroup: () => Groups.findOne('groupA')
                },
                {
                    _id: 'pB',
                    firstName: 'Bob',
                    lastName: 'Builder',
                    findDefaultGroup: () => Groups.findOne('groupB')
                }
            ]);

            const results = service.getReservationEvents(1749099600000, 1749186000000, true, '');

            const groupHeaders = results.filter(r => r.eventType === 'reservationCapacityByGroup');
            const groupNamesInOrder = groupHeaders.map(h => h.eventTargets[0]);

            // 'Beta Group' has sortOrder 1, should appear before 'Alpha Group' which has sortOrder 2
            expect(groupNamesInOrder).toEqual(['Beta Group', 'Alpha Group']);
        });

    });
});
describe('CalendarService - Additional Methods', () => {
    let service;

    beforeEach(() => {
        // Mock jQuery globally
        global.$ = jest.fn((selector) => ({
            datepicker: jest.fn().mockReturnThis(),
            on: jest.fn().mockReturnThis(),
            data: jest.fn()
        }));

        service = new CalendarService({
            timezone: 'America/Chicago',
            showSections: ['activities', 'food'],
            filterOptions: {},
            currentPerson: { type: 'staff' },
            data: {}
        });
    });

    describe('initDatepicker', () => {
        beforeEach(() => {
            jest.useFakeTimers();
        });

        afterEach(() => {
            jest.useRealTimers();
        });

        it('should initialize datepicker with correct options after timeout', () => {
            const mockElement = {
                datepicker: jest.fn().mockReturnThis(),
                on: jest.fn().mockReturnThis(),
                data: jest.fn()
            };

            global.$ = jest.fn(() => mockElement);

            service.initDatepicker('test-element');

            // Fast-forward time to trigger setTimeout
            jest.advanceTimersByTime(200);

            expect(global.$).toHaveBeenCalledWith('#test-element');
            expect(mockElement.datepicker).toHaveBeenCalledWith({ zIndexOffset: 9999});
            expect(mockElement.on).toHaveBeenCalledWith('changeDate', expect.any(Function));
        });

        it('should handle changeDate event and update range', () => {
            const mockElement = {
                datepicker: jest.fn().mockReturnThis(),
                on: jest.fn(),
                data: jest.fn()
            };

            global.$ = jest.fn(() => mockElement);
            service.recalculateRange = jest.fn();

            service.initDatepicker('test-element');
            jest.advanceTimersByTime(200);

            // Simulate changeDate event
            const changeHandler = mockElement.on.mock.calls.find(call => call[0] === 'changeDate')[1];
            const mockEvent = { date: new Date('2025-06-15') };

            changeHandler(mockEvent);

            expect(service.recalculateRange).toHaveBeenCalledWith(0);
        });
    });

    describe('recalculateRange', () => {
        beforeEach(() => {
            service.rangeStart = new ReactiveVar(moment.tz('2025-06-01', service.timezone).valueOf());
            service.viewStyle = new ReactiveVar('day');
        });

        it('should calculate day view range correctly', () => {
            service.viewStyle.set(CALENDAR_VIEWS.DAY);
            const baseDate = moment.tz('2025-06-01', service.timezone);
            service.rangeStart.set(baseDate.valueOf());

            service.recalculateRange(1);

            const expectedStart = baseDate.clone().add(1, 'day').valueOf();
            const expectedEnd = baseDate.clone().add(1, 'day').endOf('day').valueOf();

            expect(service.rangeStart.get()).toBe(expectedStart);
            expect(service.rangeEnd.get()).toBe(expectedEnd);
        });

        it('should calculate three day view range correctly', () => {
            service.viewStyle.set(CALENDAR_VIEWS.THREE_DAY);
            const baseDate = moment.tz('2025-06-01', service.timezone);
            service.rangeStart.set(baseDate.valueOf());

            service.recalculateRange(1);

            const expectedStart = baseDate.clone().add(3, 'days').valueOf();
            const expectedEnd = baseDate.clone().add(3, 'days').add(2, 'days').endOf('day').valueOf();

            expect(service.rangeStart.get()).toBe(expectedStart);
            expect(service.rangeEnd.get()).toBe(expectedEnd);
        });

        it('should calculate week view range correctly', () => {
            service.viewStyle.set(CALENDAR_VIEWS.WEEK);
            const baseDate = moment.tz('2025-06-01', service.timezone);
            service.rangeStart.set(baseDate.valueOf());

            service.recalculateRange(1);

            const expectedStart = baseDate.clone().add(7, 'days').startOf('week').add(1, 'days').valueOf();
            const expectedEnd = baseDate.clone().add(7, 'days').startOf('week').add(1, 'days').endOf('week').subtract(1, 'days').valueOf();

            expect(service.rangeStart.get()).toBe(expectedStart);
            expect(service.rangeEnd.get()).toBe(expectedEnd);
        });

        it('should calculate month view range correctly', () => {
            service.viewStyle.set(CALENDAR_VIEWS.MONTH);
            const baseDate = moment.tz('2025-06-01', service.timezone);
            service.rangeStart.set(baseDate.valueOf());

            service.recalculateRange(1);

            const expectedStart = baseDate.clone().add(1, 'month').startOf('month').valueOf();
            const expectedEnd = baseDate.clone().add(1, 'month').startOf('month').endOf('month').valueOf();

            expect(service.rangeStart.get()).toBe(expectedStart);
            expect(service.rangeEnd.get()).toBe(expectedEnd);
        });

        it('should handle negative multiplier for going backward', () => {
            service.viewStyle.set(CALENDAR_VIEWS.DAY);
            const baseDate = moment.tz('2025-06-15', service.timezone);
            service.rangeStart.set(baseDate.valueOf());

            service.recalculateRange(-5);

            const expectedStart = baseDate.clone().add(-5, 'day').valueOf();
            const expectedEnd = baseDate.clone().add(-5, 'day').endOf('day').valueOf();

            expect(service.rangeStart.get()).toBe(expectedStart);
            expect(service.rangeEnd.get()).toBe(expectedEnd);
        });

        it('should handle zero multiplier', () => {
            service.viewStyle.set(CALENDAR_VIEWS.DAY);
            const baseDate = moment.tz('2025-06-15', service.timezone);
            const originalStart = baseDate.valueOf();
            service.rangeStart.set(originalStart);

            service.recalculateRange(0);

            const expectedEnd = baseDate.clone().endOf('day').valueOf();

            expect(service.rangeStart.get()).toBe(originalStart);
            expect(service.rangeEnd.get()).toBe(expectedEnd);
        });
    });

    describe('updateFilterOptionsState', () => {
        beforeEach(() => {
            service.viewStyle = new ReactiveVar('week');
            service.reservationsSort = new ReactiveVar('group');
            service.selectedGroup = new ReactiveVar('test-group');
        });

        it('should call Meteor.callAsync with correct parameters', async () => {
            Meteor.callAsync = jest.fn().mockResolvedValue();

            await service.updateFilterOptionsState();

            expect(Meteor.callAsync).toHaveBeenCalledWith('setUiOption', 'calendarFilters', {
                reservationsViewStyle: 'week',
                reservationsSort: 'group',
                selectedGroup: 'test-group'
            });
        });

        it('should handle errors gracefully', async () => {
            const error = new Error('Network error');
            Meteor.callAsync = jest.fn().mockRejectedValue(error);

            await expect(service.updateFilterOptionsState()).rejects.toThrow('Network error');
        });
    });

    describe('shouldShow', () => {
        it('should return true when section is in show sections', () => {
            service.data = { show: 'activities,food,reservations' };

            // Mock getShowSections to return the split array
            jest.doMock('../../../../../lib/calendar/calendarUtils', () => ({
                getShowSections: jest.fn(() => ['activities', 'food', 'reservations'])
            }));

            const result = service.shouldShow('activities');
            expect(result).toBe(true);
        });

        it('should return false when section is not in show sections', () => {
            service.data = { show: 'activities,food' };

            jest.doMock('../../../../../lib/calendar/calendarUtils', () => ({
                getShowSections: jest.fn(() => ['activities', 'food'])
            }));

            const result = service.shouldShow('reservations');
            expect(result).toBe(false);
        });

        it('should handle null/undefined show sections gracefully', () => {
            service.data = {};

            jest.doMock('../../../../../lib/calendar/calendarUtils', () => ({
                getShowSections: jest.fn(() => [])
            }));

            const result = service.shouldShow('activities'); // Activities is a default show section when data is empty
            expect(result).toBe(true);
        });
    });

    describe('loadData', () => {
        beforeEach(() => {
            service.rangeStart = new ReactiveVar(moment.tz('2025-06-01', service.timezone).valueOf());
            service.rangeEnd = new ReactiveVar(moment.tz('2025-06-07', service.timezone).valueOf());
            service.curriculumData = new ReactiveVar([]);
            service.curriculumThemesData = new ReactiveVar([]);

            Meteor.subscribe = jest.fn();
            Meteor.callAsync = jest.fn();
        });

        it('should subscribe to all required collections', async () => {
            const mockCurriculumData = [{ _id: 'c1', name: 'Test Curriculum' }];
            const mockThemesData = [{ _id: 't1', name: 'Test Theme' }];

            Meteor.callAsync
                .mockResolvedValueOnce(mockCurriculumData)
                .mockResolvedValueOnce(mockThemesData);

            await service.loadData();

            expect(Meteor.subscribe).toHaveBeenCalledWith('theFood', {
                startDate: '06/01/2025',
                endDate: '06/08/2025'
            });

            expect(Meteor.subscribe).toHaveBeenCalledWith('theReservations', {
                startDate: '06/01/2025',
                endDate: '06/08/2025'
            });

            expect(Meteor.subscribe).toHaveBeenCalledWith('theAnnouncements', {
                rangeStart: service.rangeStart.get(),
                rangeEnd: service.rangeEnd.get(),
                expand: true
            });
        });

        it('should fetch curriculum and themes data', async () => {
            const mockCurriculumData = [{ _id: 'c1', name: 'Test Curriculum' }];
            const mockThemesData = [{ _id: 't1', name: 'Test Theme' }];

            Meteor.callAsync
                .mockResolvedValueOnce(mockCurriculumData)
                .mockResolvedValueOnce(mockThemesData);

            await service.loadData();

            expect(Meteor.callAsync).toHaveBeenCalledWith('getCurriculumData', {
                startDate: '06/01/2025',
                endDate: '06/08/2025'
            });

            expect(Meteor.callAsync).toHaveBeenCalledWith('getCurriculumThemesData', {
                startDate: '06/01/2025',
                endDate: '06/08/2025'
            });

            expect(service.curriculumData.get()).toEqual(mockCurriculumData);
            expect(service.curriculumThemesData.get()).toEqual(mockThemesData);
        });

        it('should handle API errors gracefully', async () => {
            const error = new Error('API Error');
            Meteor.callAsync.mockRejectedValue(error);

            await expect(service.loadData()).rejects.toThrow('API Error');
        });
    });

    describe('getPeopleForReservations', () => {
        beforeEach(() => {
            service.rangeStart = new ReactiveVar(moment.tz('2025-06-01', service.timezone).valueOf());
            service.rangeEnd = new ReactiveVar(moment.tz('2025-06-07', service.timezone).valueOf());
            service.showContent = new ReactiveVar(true);
            service.allReservationsPeopleLoader = new ReactiveVar(false);
            service.allReservationsPeople = new ReactiveVar([]);
            service.currentPerson = {
                findInheritedRelationships: jest.fn(() => [
                    { targetId: 'person1' },
                    { targetId: 'person2' }
                ])
            };

            // Mock Reservations.findWithRecurrence
            Reservations.findWithRecurrence.mockReturnValue([
                    { selectedPerson: 'person1' },
                    { selectedPerson: 'person3' }
                ]);
        });

        it('should set loading states correctly', async () => {
            const { getPeopleData } = require('../../../../../client/services/peopleMeteorService');
            getPeopleData.mockResolvedValueOnce([
                { _id: 'person1', name: 'John' },
                { _id: 'person2', name: 'Jane' },
                { _id: 'person3', name: 'Bob' }
            ]);

            expect(service.showContent.get()).toBe(true);
            expect(service.allReservationsPeopleLoader.get()).toBe(false);

            const loadPromise = service.getPeopleForReservations();

            // During loading
            expect(service.showContent.get()).toBe(false);
            expect(service.allReservationsPeopleLoader.get()).toBe(true);

            await loadPromise;

            // After loading
            expect(service.showContent.get()).toBe(true);
            expect(service.allReservationsPeopleLoader.get()).toBe(false);
        });

        it('should fetch people for reservations and relationships', async () => {
            const { getPeopleData } = require('../../../../../client/services/peopleMeteorService');
            const mockPeople = [
                { _id: 'person1', name: 'John' },
                { _id: 'person2', name: 'Jane' },
                { _id: 'person3', name: 'Bob' }
            ];
            getPeopleData.mockResolvedValueOnce(mockPeople);

            await service.getPeopleForReservations();

            expect(getPeopleData).toHaveBeenCalledWith(
                { _id: { $in: ['person1', 'person3', 'person2'] } },
                {}
            );
            expect(service.allReservationsPeople.get()).toEqual(mockPeople);
        });

        it('should handle case when currentPerson has no relationships', async () => {
            service.currentPerson = {
                findInheritedRelationships: jest.fn(() => [])
            };

            const { getPeopleData } = require('../../../../../client/services/peopleMeteorService');
            getPeopleData.mockResolvedValueOnce([{ _id: 'person3', name: 'Bob' }]);

            await service.getPeopleForReservations();

            expect(getPeopleData).toHaveBeenCalledWith(
                { _id: { $in: ['person1', 'person3'] } },
                {}
            );
        });

        it('should handle case when currentPerson is null', async () => {
            service.currentPerson = null;

            const { getPeopleData } = require('../../../../../client/services/peopleMeteorService');
            getPeopleData.mockResolvedValue([{ _id: 'person3', name: 'Bob' }]);

            await service.getPeopleForReservations();

            expect(getPeopleData).toHaveBeenCalledWith(
                { _id: { $in: ['person1', 'person3'] } },
                {}
            );
        });

        it('should handle API errors gracefully', async () => {
            const { getPeopleData } = require('../../../../../client/services/peopleMeteorService');
            const error = new Error('People API Error');
            getPeopleData.mockRejectedValue(error);

            await expect(service.getPeopleForReservations()).rejects.toThrow('People API Error');

            // Should reset loading states even on error
            expect(service.allReservationsPeopleLoader.get()).toBe(false);
        });

        it('should deduplicate person IDs correctly', async () => {
            service.currentPerson = {
                findInheritedRelationships: jest.fn(() => [
                    { targetId: 'person1' }, // Duplicate with reservation
                    { targetId: 'person1' }, // Duplicate relationship
                    { targetId: 'person2' }
                ])
            };

            Reservations.findWithRecurrence.mockReturnValue([
                { selectedPerson: 'person1' },
                { selectedPerson: 'person1' }, // Duplicate reservation
                { selectedPerson: 'person3' }
            ]);

            const { getPeopleData } = require('../../../../../client/services/peopleMeteorService');
            getPeopleData.mockResolvedValue([]);

            await service.getPeopleForReservations();

            expect(getPeopleData).toHaveBeenCalledWith(
                { _id: { $in: ['person1', 'person3', 'person2'] } },
                {}
            );
        });
    });

    describe('Private Helper Methods', () => {
        describe('_getRangeStart', () => {
            it('should return query param value when present', () => {
                FlowRouter.getQueryParam.mockReturnValue('1625097600000');

                const result = service._getRangeStart();

                expect(result).toBe(1625097600000);
                expect(FlowRouter.getQueryParam).toHaveBeenCalledWith('rangeStart');
            });

            it('should return current day start when no query param', () => {
                FlowRouter.getQueryParam.mockReturnValue(null);

                const result = service._getRangeStart();
                const expected = moment.tz(service.timezone).startOf('day').valueOf();

                expect(result).toBe(expected);
            });

            it('should handle invalid query param', () => {
                FlowRouter.getQueryParam.mockReturnValue('invalid');

                const result = service._getRangeStart();
                const expected = moment.tz(service.timezone).startOf('day').valueOf();

                expect(result).toBe(expected);
            });
        });

        describe('_getRangeEnd', () => {
            it('should return query param value when present', () => {
                FlowRouter.getQueryParam.mockReturnValue('1625183999999');

                const result = service._getRangeEnd();

                expect(result).toBe(1625183999999);
                expect(FlowRouter.getQueryParam).toHaveBeenCalledWith('rangeEnd');
            });

            it('should return current day end when no query param', () => {
                FlowRouter.getQueryParam.mockReturnValue(null);

                const result = service._getRangeEnd();
                const expected = moment.tz(service.timezone).endOf('day').valueOf();

                expect(result).toBe(expected);
            });
        });

        describe('_getViewStyle', () => {
            beforeEach(() => {
                service.showSections = [];
                service.filterOptions = {};
            });

            it('should return reservationsViewStyle when reservations section is shown', () => {
                service.showSections = ['reservations'];
                service.filterOptions = { reservationsViewStyle: 'month' };

                const result = service._getViewStyle();

                expect(result).toBe('month');
            });

            it('should fall back to week view when reservations section shown but no style set', () => {
                service.showSections = ['reservations'];
                service.filterOptions = {};

                const result = service._getViewStyle();

                expect(result).toBe('week');
            });

            it('should return query param when reservations section not shown', () => {
                service.showSections = ['activities'];
                FlowRouter.getQueryParam.mockReturnValue('day');

                const result = service._getViewStyle();

                expect(result).toBe('day');
                expect(FlowRouter.getQueryParam).toHaveBeenCalledWith('viewStyle');
            });

            it('should return default day view when no query param and no reservations', () => {
                service.showSections = ['activities'];
                FlowRouter.getQueryParam.mockReturnValue(null);

                const result = service._getViewStyle();

                expect(result).toBe('day');
            });
        });

        describe('_getSelectedGroup', () => {
            it('should return query param for non-staff/admin users', () => {
                service.currentPerson = { type: 'family' };
                FlowRouter.getQueryParam.mockReturnValue('group123');

                const result = service._getSelectedGroup();

                expect(result).toBe('group123');
            });

            it('should return query param when in print view', () => {
                service.currentPerson = { type: 'staff' };
                FlowRouter.getQueryParam
                    .mockReturnValueOnce('print') // view param
                    .mockReturnValueOnce('group456'); // group param

                const result = service._getSelectedGroup();

                expect(result).toBe('group456');
            });

            it('should return checkInGroupId for staff when available', () => {
                service.currentPerson = {
                    type: 'staff',
                    checkInGroupId: 'checkin123',
                    defaultGroupId: 'default456'
                };
                FlowRouter.getQueryParam.mockReturnValue(null);

                const result = service._getSelectedGroup();

                expect(result).toBe('checkin123');
            });

            it('should fall back to defaultGroupId for staff', () => {
                service.currentPerson = {
                    type: 'staff',
                    defaultGroupId: 'default456'
                };
                FlowRouter.getQueryParam.mockReturnValue(null);

                const result = service._getSelectedGroup();

                expect(result).toBe('default456');
            });

            it('should return query param as final fallback for staff', () => {
                service.currentPerson = { type: 'staff' };
                FlowRouter.getQueryParam.mockReturnValue('fallback789');

                const result = service._getSelectedGroup();

                expect(result).toBe('fallback789');
            });

            it('should return empty string when no person', () => {
                service.currentPerson = null;
                FlowRouter.getQueryParam.mockReturnValue(null);

                const result = service._getSelectedGroup();

                expect(result).toBe('');
            });
        });
    });
});

