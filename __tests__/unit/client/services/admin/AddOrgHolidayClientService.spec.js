import { AddHolidayClientService } from '../../../../../client/services/admin/AddOrgHolidayClientService';
import { HOLIDAY_SCHEDULE_TYPES, HOLIDAY_TYPES } from '../../../../../lib/constants/holidayConstants';
import { jest, describe, beforeEach, it, expect } from '@jest/globals';
import { AvailableTimezoneMap } from '../../../../../lib/constants/timezoneConstants';
import { AvailableCustomizations } from '../../../../../lib/customizations';

global.mpSwal = { fire: jest.fn() };
global.Meteor = { callAsync: jest.fn().mockResolvedValue() };
global.Random = { id: jest.fn(() => 'random-id') };

// Mock jQuery
const $mock = jest.fn(() => ({
    val: jest.fn(),
    trigger: jest.fn(),
    find: jest.fn(() => ({ remove: jest.fn() })),
    append: jest.fn(),
    data: jest.fn(),
}));
global.$ = $mock;
describe('AddHolidayClientService', () => {
    let service;
    let mockOrg;
    const getTimezoneMock = jest.fn().mockReturnValue('America/New_York');
    const hasCustomizationMock = jest.fn((customization) => {
        return mockOrg.customizations && mockOrg.customizations[customization] === true;
    });

    beforeEach(() => {
        mockOrg = {
            _id: 'org1',
            getHolidays: () => [
                { _id: 'h1', name: 'Holiday A', date: '2024-12-25', dateType: HOLIDAY_TYPES.INDIVIDUAL }
            ],
            getScheduleTypes: () => [
                { _id: 'st1', type: 'Full Day' },
                { _id: 'st2', type: 'Half Day' }
            ],
            getTimezone: getTimezoneMock,
            hasCustomization: hasCustomizationMock,
        };

        service = new AddHolidayClientService(mockOrg);
        jest.clearAllMocks();
    });

    describe('constructor', () => {
        it('should initialize with holidays and schedule types', () => {
            expect(service.getCurrentOrg()).toBe(mockOrg);
            expect(service.getHolidays().length).toBe(1);
            expect(service.getScheduleTypes().length).toBe(2);
        });
    });

    describe('buildScheduleTypeSelectOptions', () => {
        it('should populate select list with schedule types', () => {
            const mockRemove = jest.fn();
            const mockNot = jest.fn(() => ({ not: jest.fn(() => ({ remove: mockRemove })) }));
            const selectList = {
                find: jest.fn(() => ({ not: mockNot })),
                append: jest.fn(),
                val: jest.fn().mockReturnThis(),
                trigger: jest.fn(),
            };

            service.buildScheduleTypeSelectOptions(selectList);

            // Adjust expectation to match actual scheduleTypes
            const expectedTypes = mockOrg.getScheduleTypes();
            expect(selectList.append).toHaveBeenCalledTimes(expectedTypes.length);
            expect(selectList.val).toHaveBeenCalledWith([HOLIDAY_SCHEDULE_TYPES.NONE]);
            expect(selectList.trigger).toHaveBeenCalledWith('change');
        });

    });

    describe('displayHolidayDate', () => {
        const timezones = Object.keys(AvailableTimezoneMap);
        it('should format individual holiday date', () => {
            for (const timezone of timezones) {
                getTimezoneMock.mockReturnValue(timezone);
                const date = service.displayHolidayDate({
                    date: '2024-12-25',
                    dateType: HOLIDAY_TYPES.INDIVIDUAL
                });
                expect(date).toBe('12/25/2024');
            }
        });

        it('should format holiday range dates', () => {
            for (const timezone of timezones) {
                getTimezoneMock.mockReturnValue(timezone);
                const date = service.displayHolidayDate({
                    startDate: '2024-12-22',
                    endDate: '2025-01-01',
                    dateType: HOLIDAY_TYPES.RANGE
                });
                expect(date).toBe('12/22/2024 - 01/01/2025');
            }
        });
    });

    describe('validateHoliday', () => {
        it('should return error for missing name', () => {
            const result = service.validateHoliday({ dateType: HOLIDAY_TYPES.INDIVIDUAL, scheduleTypes: ['st1'], date: '2024-12-25' });
            expect(result).toMatch(/name is required/i);
        });

        it('should return false for valid individual holiday', () => {
            const result = service.validateHoliday({
                name: 'Test',
                dateType: HOLIDAY_TYPES.INDIVIDUAL,
                scheduleTypes: ['st1'],
                date: '2024-12-25',
            });
            expect(result).toBe(false);
        });

        it('should error if "None" is combined with other schedule types', () => {
            const result = service.validateHoliday({
                name: 'Test',
                dateType: HOLIDAY_TYPES.INDIVIDUAL,
                scheduleTypes: ['st1', HOLIDAY_SCHEDULE_TYPES.NONE],
                date: '2024-12-25',
            });
            expect(result).toMatch(/Cannot select "None"/);
        });
    });

    describe('addUpdateHoliday', () => {
        it('should call Meteor method and not crash on missing DOM', async () => {
            const holiday = {
                _id: 'h1',
                name: 'Holiday A',
                dateType: HOLIDAY_TYPES.INDIVIDUAL,
                date: '2024-12-25',
                scheduleTypes: ['st1'],
                autoProrate: true,
            };

            // Mock jQuery selectors used in addUpdateHoliday
            const mockForm = {
                hide: jest.fn(),
                reset: jest.fn(),
                0: { reset: jest.fn() }, // for form[0].reset()
            };
            const $mock = jest.fn((selector) => {
                if (selector === '#frmAddHoliday') return mockForm;
                if (selector === '#confirmHolidayModal') return { modal: jest.fn() };
                if (selector === '#btnAddHoliday') return { show: jest.fn() };
                return { hide: jest.fn(), show: jest.fn(), reset: jest.fn(), val: jest.fn() };
            });
            global.$ = $mock;

            await service.addUpdateHoliday(holiday);

            expect(Meteor.callAsync).toHaveBeenCalledWith('insertUpdateHoliday', {holidayObject: holiday, isNew: false});
            expect(mpSwal.fire).toHaveBeenCalledWith({"icon": "success", "text": "Holiday saved successfully", "title": "Success"});
        });

        it('should show error if holiday is null', async () => {
            await service.addUpdateHoliday(null);
            expect(mpSwal.fire).toHaveBeenCalledWith('Error', 'Holiday data is required to add or update.', 'error');
        });
    });

    describe('deleteHoliday', () => {
        it('should call Meteor method with holidayId', async () => {
            await service.deleteHoliday('h123');
            expect(Meteor.callAsync).toHaveBeenCalledWith('removeHoliday', { holidayId: 'h123' });
        });

        it('should show error if holidayId is missing', async () => {
            await service.deleteHoliday();
            expect(mpSwal.fire).toHaveBeenCalledWith('Error', 'Holiday ID is required to delete.', 'error');
        });
    });

    describe('orgHasAutoProrateBySchedule', () => {
        it('should return true if org has AUTO_PRORATE_BY_SCHEDULE customization', () => {
            mockOrg.customizations = {
                [AvailableCustomizations.AUTO_PRORATE_BY_SCHEDULE]: true,
            }

            const result = service.orgHasAutoProrateBySchedule();
            expect(result).toBe(true);
            expect(mockOrg.hasCustomization).toHaveBeenCalledWith(AvailableCustomizations.AUTO_PRORATE_BY_SCHEDULE);
        });

        it('should return false if org is null or does not have the customization', () => {
            // Case 1: Org is null
            service.getCurrentOrg = () => null;
            expect(service.orgHasAutoProrateBySchedule()).toBe(false);

            // Case 2: Org exists but doesn't have customization
            service.getCurrentOrg = () => ({
                hasCustomization: () => false
            });
            expect(service.orgHasAutoProrateBySchedule()).toBe(false);
        });
    });
});