import { Orgs } from '../../../../../lib/collections/orgs';
import { OrgHolidayAuditHandler } from '../../../../../server/audit/handlers/OrgHolidayAuditHandler';
import { AUDIT_METHOD_NAMES } from '../../../../../lib/audit/auditTrailConstants';

jest.mock('../../../../../lib/collections/orgs', () => ({
    Orgs: {
        findOneAsync: jest.fn(),
    },
}));


describe('OrgHolidayAuditHandler', () => {
    const mockUser = {
        _id: 'user123',
        personId: 'person123',
        orgId: 'org456',
        fetchPerson: jest.fn().mockResolvedValue({
            firstName: 'Test',
            lastName: 'User',
        }),
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should log a Created holiday', async () => {
        const args = {
            isNew: true,
            holidayObject: {
                name: 'New Holiday',
                dateType: 'individual',
                date: '2025-12-25',
                startDate: '2025-12-25',
                endDate: '2025-12-25',
                scheduleTypes: ['All'],
                autoProrate: true,
            },
        };

        const handler = new OrgHolidayAuditHandler({
            methodName: AUDIT_METHOD_NAMES.ORG_HOLIDAYS.INSERT_UPDATE,
            args,
            user: mockUser,
        });

        Orgs.findOneAsync.mockResolvedValue({ _id: 'org456', valueOverrides: { holidays: [] } });

        const result = await handler.getLogData();

        expect(result).toMatchObject({
            userId: 'user123',
            personId: 'person123',
            orgId: 'org456',
            performedBy: 'Test User',
            methodName: AUDIT_METHOD_NAMES.ORG_HOLIDAYS.INSERT_UPDATE,
            type: 'Holiday',
            actionType: 'Created',
            details: args.holidayObject,
        });
    });

    it('should log an Edited holiday', async () => {
        const args = {
            isNew: false,
            holidayObject: {
                name: 'Updated Holiday',
                dateType: 'individual',
                date: '2025-12-26',
                startDate: '2025-12-26',
                endDate: '2025-12-26',
                scheduleTypes: ['A'],
                autoProrate: false,
            },
        };

        const handler = new OrgHolidayAuditHandler({
            methodName: AUDIT_METHOD_NAMES.ORG_HOLIDAYS.INSERT_UPDATE,
            args,
            user: mockUser,
        });

        Orgs.findOneAsync.mockResolvedValue({ _id: 'org456', valueOverrides: { holidays: [] } });

        const result = await handler.getLogData();

        expect(result.actionType).toBe('Edited');
        expect(result.details.name).toBe('Updated Holiday');
    });

    it('should log a Deleted holiday using snapshot from org config', async () => {
        const args = {
            holidayId: 'h123',
        };

        const orgDoc = {
            _id: 'org456',
            valueOverrides: {
                holidays: [
                    {
                        _id: 'h123',
                        name: 'Removed Holiday',
                        dateType: 'individual',
                        date: '2025-01-01',
                        startDate: '2025-01-01',
                        endDate: '2025-01-01',
                        scheduleTypes: ['B'],
                        autoProrate: true,
                    },
                ],
            },
        };

        const handler = new OrgHolidayAuditHandler({
            methodName: AUDIT_METHOD_NAMES.ORG_HOLIDAYS.REMOVE,
            args,
            user: mockUser,
        });

        Orgs.findOneAsync.mockResolvedValue(orgDoc);

        const result = await handler.getLogData();

        expect(result.actionType).toBe('Deleted');
        expect(result.details).toEqual(expect.objectContaining({
            name: 'Removed Holiday',
            scheduleTypes: ['B'],
            autoProrate: true,
        }));
    });
});
