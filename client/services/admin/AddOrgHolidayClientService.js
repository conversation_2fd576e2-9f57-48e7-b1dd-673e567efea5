import { ReactiveVar } from 'meteor/reactive-var';
import { Random } from 'meteor/random';
import { Orgs } from '../../../lib/collections/orgs';
import moment from 'moment-timezone';
import { HOLIDAY_SCHEDULE_TYPES, HOLIDAY_TYPES } from '../../../lib/constants/holidayConstants';
import $ from 'jquery';
import { AvailableCustomizations } from '../../../lib/customizations';

/**
 * Service class for managing holiday-related UI state and logic in the Organization Admin panel.
 * Handles form input parsing, schedule type list behavior, validation, and calls to server methods.
 */
export class AddHolidayClientService {
    /**
     * @param {Object} org - The current organization object with `getHolidays()` and `getScheduleTypes()` methods.
     */
    constructor(org) {
        this.holidays = new ReactiveVar([]);
        this.scheduleTypes = new ReactiveVar([]);
        this.currentOrg = new ReactiveVar(null);
        this.selectedScheduleTypes = new ReactiveVar([HOLIDAY_SCHEDULE_TYPES.NONE]);

        if (org) {
            this.currentOrg.set(org);
            this.holidays.set(org.getHolidays());
            this.scheduleTypes.set(org.getScheduleTypes());
        }
    }

    /**
     * Populates a schedule type `<select>` element with org-specific schedule type options.
     * Preserves static "None" and "All" options.
     *
     * @param {JQuery} selectList - The jQuery-wrapped select element to populate.
     * @param {string[]} [selected=[HOLIDAY_SCHEDULE_TYPES.NONE]] - Values to preselect.
     */
    buildScheduleTypeSelectOptions(selectList, selected = [HOLIDAY_SCHEDULE_TYPES.NONE]) {
        selectList.find('option').not('[value="None"]').not('[value="All"]').remove();
        const scheduleTypes = this.getScheduleTypes();

        scheduleTypes.forEach(st => {
            selectList.append(`<option value="${st._id}">${st.type}</option>`);
        });

        selectList.val(selected).trigger('change');
    }

    /**
     * @returns {Object[]} - The current org's holiday objects.
     */
    getHolidays() {
        return this.holidays.get();
    }

    orgHasAutoProrateBySchedule() {
        const org = this.getCurrentOrg();
        if (!org) {
            return false;
        }

        return org.hasCustomization(AvailableCustomizations.AUTO_PRORATE_BY_SCHEDULE);
    }

    /**
     * @returns {Object[]} - The current org's schedule type objects.
     */
    getScheduleTypes() {
        return this.scheduleTypes.get();
    }

    /**
     * @returns {Object|null} - The currently selected org object.
     */
    getCurrentOrg() {
        return this.currentOrg.get();
    }

    /**
     * Updates internal ReactiveVars with a new org's data.
     *
     * @param {Object} org - The new organization object.
     */
    updateCurrentOrg(org) {
        if (!org) {
            return;
        }

        this.currentOrg.set(org);
        this.holidays.set(org.getHolidays());
        this.scheduleTypes.set(org.getScheduleTypes());
    }

    /**
     * Formats a date for display in holiday tables.
     *
     * @param {string|Date|moment.Moment} date - The date to format.
     * @param {string} timezone - The org timezone.
     * @returns {string} - Formatted date (MM/DD/YYYY) or empty string if invalid.
     */
    formatHolidayDateForTable(date, timezone) {
        return date ? moment.tz(date, 'YYYY-MM-DD', timezone).format('MM/DD/YYYY') : '';
    }

    /**
     * Returns a formatted holiday date string based on its date type (individual or range).
     *
     * @param {Object} holiday - The holiday object.
     * @returns {string} - A string like "12/25/2024" or "12/22/2024 - 01/02/2025".
     */
    displayHolidayDate(holiday) {
        const type = holiday.dateType || HOLIDAY_TYPES.INDIVIDUAL;
        const timezone = this.getCurrentOrg()?.getTimezone() || 'America/New_York';

        if( type === HOLIDAY_TYPES.INDIVIDUAL) {
            return this.formatHolidayDateForTable(holiday.date, timezone);
        } else if (type === HOLIDAY_TYPES.RANGE) {
            return holiday.startDate && holiday.endDate ?
                `${this.formatHolidayDateForTable(holiday.startDate, timezone)} - ${this.formatHolidayDateForTable(holiday.endDate, timezone)}` : '';
        }

        return '';
    }

    /**
     * Resolves and formats the permitted schedule types for a given holiday.
     *
     * @param {Object} holiday - The holiday object.
     * @returns {string} - "None", "All", or a comma-separated string of schedule type names.
     */
    getPermittedScheduleTypes(holiday) {
        const scheduleTypes = this.getScheduleTypes() || [];

        if (!holiday) {
            return '';
        }

        if (!holiday.scheduleTypes || !Array.isArray(holiday.scheduleTypes)) {
            return HOLIDAY_SCHEDULE_TYPES.NONE;
        }

        if (holiday.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.NONE)) {
            return HOLIDAY_SCHEDULE_TYPES.NONE;
        } else if (holiday.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.ALL)) {
            return HOLIDAY_SCHEDULE_TYPES.ALL
        } else {
            return scheduleTypes.filter(st => holiday.scheduleTypes.includes(st._id)).map(st => st.type.trim()).join(', ') || HOLIDAY_SCHEDULE_TYPES.NONE;
        }
    }

    /**
     * @param {Object} holiday - The holiday object.
     * @returns {string} - "Yes" or "No" depending on autoProrate flag (defaults to Yes).
     */
    getAutoProrate(holiday) {
        if (!holiday) {
            return '';
        }

        if (holiday.autoProrate == null) { // For legacy holidays that may not have this field
            return 'Yes';
        }

        return holiday.autoProrate ? 'Yes' : 'No';
    }

    /**
     * @returns {JQuery} - jQuery-wrapped #holidayScheduleTypes select element.
     */
    getScheduleSelectList() {
        return $("#holidayScheduleTypes");
    }

    /**
     * Validates a holiday object for required fields and logical consistency.
     *
     * @param {Object} holiday - The holiday object to validate.
     * @returns {string|false} - Error message string if invalid, or false if valid.
     */
    validateHoliday(holiday) {
        if (!holiday.name) {
            return 'Holiday name is required.';
        }

        if (!holiday.dateType) {
            return 'Holiday date type is required.';
        }

        if (![HOLIDAY_TYPES.INDIVIDUAL, HOLIDAY_TYPES.RANGE].includes(holiday.dateType)) {
            return `Invalid holiday date type. Must be either ${HOLIDAY_TYPES.INDIVIDUAL} or ${HOLIDAY_TYPES.RANGE}.`;
        }

        if (!Array.isArray(holiday.scheduleTypes) || holiday.scheduleTypes.length === 0) {
            return 'At least one schedule type must be selected for the holiday.';
        }

        if (holiday.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.NONE) && holiday.scheduleTypes.length > 1) {
            return `Cannot select "${HOLIDAY_SCHEDULE_TYPES.NONE}" schedule type with other schedule types.`;
        }

        if (holiday.dateType === HOLIDAY_TYPES.INDIVIDUAL && (!holiday.date || !moment(holiday.date).isValid())) {
            return 'Holiday date is required for individual holidays.';
        }

        if (holiday.dateType === HOLIDAY_TYPES.RANGE && ((!holiday.startDate || !moment(holiday.startDate).isValid()) || (!holiday.endDate || !moment(holiday.endDate).isValid()))) {
            return 'Both start and end dates are required for date range holidays.';
        }

        if (holiday.dateType === HOLIDAY_TYPES.RANGE && moment(holiday.startDate).isAfter(moment(holiday.endDate))) {
            return 'Start date cannot be after end date for date range holidays.';
        }

        return false;
    }

    /**
     * Reads the current holiday form fields and constructs a holiday object.
     *
     * @returns {Object} - The generated holiday object.
     */
    generateHolidayObject() {
        const dateType = $("#holidayDateType").val() || HOLIDAY_TYPES.INDIVIDUAL;
        const isIndividual = dateType === HOLIDAY_TYPES.INDIVIDUAL;
        const existingId = $("input[name=holiday-id]").val();
        const timezone = this.getCurrentOrg()?.getTimezone() || 'America/New_York';

        const date = moment.tz($("#holidayDate").val(), 'MM/DD/YYYY', timezone).format('YYYY-MM-DD');
        const startDate = moment.tz($("#holidayStartDate").val(), 'MM/DD/YYYY', timezone).format('YYYY-MM-DD');
        const endDate = moment.tz($("#holidayEndDate").val(), 'MM/DD/YYYY', timezone).format('YYYY-MM-DD');
        return {
            _id: existingId || Random.id(),
            name: $("#AddHoliday-Name").val().trim(),
            dateType,
            date: isIndividual ? date : startDate,
            startDate: isIndividual ? date : startDate,
            endDate: isIndividual ? date : endDate,
            scheduleTypes: this.getScheduleSelectList().val(),
            autoProrate: $("#holidayAutoProrate").val() === 'true',
            isNew : !existingId, // Determine if this is a new holiday
        }
    }

    /**
     * Handles complex multi-select logic for schedule types:
     * - "None" deselects all other options.
     * - Selecting another value deselects "None".
     * - Selecting "All" selects all schedule types.
     * - Removing a type deselects "All".
     */
    handleSelectListChange() {
        const selectList = this.getScheduleSelectList();
        if (!selectList) {
            return;
        }

        const selected = selectList.val() || [];

        if (!Array.isArray(selected) || selected.length === 0) {
            this.selectedScheduleTypes.set([HOLIDAY_SCHEDULE_TYPES.NONE]);
            selectList.val([HOLIDAY_SCHEDULE_TYPES.NONE]).trigger('change.select2');
            return;
        }

        const previousSelected = this.selectedScheduleTypes.get();

        const hasNone = selected.includes(HOLIDAY_SCHEDULE_TYPES.NONE);
        const noneWasSelected = !previousSelected.includes(HOLIDAY_SCHEDULE_TYPES.NONE) && hasNone;
        const hasAll = selected.includes(HOLIDAY_SCHEDULE_TYPES.ALL);
        const allWasSelected = !previousSelected.includes(HOLIDAY_SCHEDULE_TYPES.ALL) && hasAll;

        const scheduleTypes = this.getScheduleTypes().map(st => st._id) || [];

        // Handle "None" logic
        if (noneWasSelected && selected.length > 1) {
            // Deselect everything else if None is selected
            this.selectedScheduleTypes.set([HOLIDAY_SCHEDULE_TYPES.NONE]);
            selectList.val([HOLIDAY_SCHEDULE_TYPES.NONE]).trigger('change.select2');
            return;
        }

        // Edge case in which none is deselected and saved
        if (!noneWasSelected && selected.length === 1 && hasNone) {
          this.selectedScheduleTypes.set([HOLIDAY_SCHEDULE_TYPES.NONE]);
          selectList.val([HOLIDAY_SCHEDULE_TYPES.NONE]).trigger('change.select2');
          return;
        }

        // If another option is selected, deselect None
        if (hasNone && selected.length > 0 && !noneWasSelected) {
            if (allWasSelected) {
                // If "All" was selected, we need to keep it and add the other schedule types
                this.selectedScheduleTypes.set([HOLIDAY_SCHEDULE_TYPES.ALL, ...scheduleTypes]);
                selectList.val([HOLIDAY_SCHEDULE_TYPES.ALL, ...scheduleTypes]).trigger('change.select2');
                return;
            }

            const updated = selected.filter(val => val !== HOLIDAY_SCHEDULE_TYPES.NONE);
            this.selectedScheduleTypes.set(updated);
            selectList.val(updated).trigger('change.select2');
            return;
        }

        // Handle "All" logic
        if (allWasSelected) {
            const fullSelection = [HOLIDAY_SCHEDULE_TYPES.ALL, ...scheduleTypes];
            this.selectedScheduleTypes.set(fullSelection);
            selectList.val(fullSelection).trigger('change.select2');
            return;
        }

        // If "All" is selected but one of the org schedule types is removed, deselect All
        if (hasAll && [HOLIDAY_SCHEDULE_TYPES.ALL, ...scheduleTypes].length > selected.length) {
            const stillAllSelected = scheduleTypes.every(id => selected.includes(id));
            if (!stillAllSelected && selected.includes(HOLIDAY_SCHEDULE_TYPES.ALL)) {
                const updated = selected.filter(val => val !== HOLIDAY_SCHEDULE_TYPES.ALL);
                this.selectedScheduleTypes.set(updated);
                selectList.val(updated).trigger('change.select2');
            }
        }
    }

    /**
     * Calls a Meteor method to insert or update a holiday.
     * Displays success/failure UI feedback and resets the form on success.
     *
     * @param {Object} holiday - The holiday object to add or update.
     * @returns {Promise<void>}
     */
    async addUpdateHoliday(holiday) {
        if (!holiday) {
            return mpSwal.fire('Error', 'Holiday data is required to add or update.', 'error');
        }
        const isNew = holiday.isNew === true;
        delete holiday.isNew; // Remove isNew flag before sending to server

        Meteor.callAsync("insertUpdateHoliday", { holidayObject: holiday, isNew }).then(() => {
            mpSwal.fire({
                icon: "success",
                title: "Success",
                text: "Holiday saved successfully"
            });
            this.updateCurrentOrg(Orgs.current());

            $("#confirmHolidayModal").modal('hide');
            const form = $("#frmAddHoliday");
            form.hide();
            form[0].reset();
            $("#btnAddHoliday").show();
        })
        .catch(error => {
            mpSwal.fire({
                icon: "error",
                title: "Error",
                text: error.message || "Failed to save holiday"
            });
        });
    }

    /**
     * Calls a Meteor method to remove a holiday.
     * Displays success/failure UI feedback.
     *
     * @param {string} holidayId - The `_id` of the holiday to delete.
     * @returns {Promise<void>}
     */
    async deleteHoliday(holidayId) {
        if (!holidayId) {
            return mpSwal.fire('Error', 'Holiday ID is required to delete.', 'error');
        }

        Meteor.callAsync('removeHoliday', {holidayId}).then(() => {
            mpSwal.fire("Holiday removed");
        })
        .catch(error => {
            mpSwal.fire("Error removing holiday", error.message, "error");
        });
    }
}