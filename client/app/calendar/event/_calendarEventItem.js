import { Template } from 'meteor/templating';
import { Session } from 'meteor/session';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { showModal } from '../../main';
import moment from "moment-timezone";
import './_calendarEventItem.html';

Template._calendarEventItem.helpers({
    itemIcon(item) {
        let iconName = "";
        switch (item.eventType || item.title) {
            case "activities":
                iconName = "fa-graduation-cap";
                break;
            case "announcement":
            case "announcements":
                iconName = "fa-bullhorn";
                break;
            case "food":
                iconName = "fa-utensils";
                break;
            case "reservationSlot":
            case "holiday":
                break;
        }

        if (iconName !== "")
            return iconName;
    },

    jsonify(item) {
        return encodeURIComponent(JSON.stringify(item));
    },

    displaySubTitle(passedItem) {
        const item = (passedItem && passedItem.eventTargets) || [];
        return (item.length === 1 && item[0] !== passedItem.title);
    },

    isPrintMode() {
        return FlowRouter.current().queryParams['view'] === 'print';
    }
});

Template._calendarEventItem.events({
    "click .calendar-item": function (event) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const itemDate = $(event.currentTarget).data("date");
        const timezone = service.timezone;

        const calEvent = JSON.parse(decodeURIComponent($(event.currentTarget).data("event")));
        if (calEvent) {
            Session.set("eventDetailModalData", {
                title: calEvent.title,
                detail: calEvent.eventDetail,
                subDetail: calEvent.eventSubDetail,
                headerName: calEvent.eventHeader,
                scheduledFor: moment.tz(calEvent.start, timezone).format("MM/DD/YYYY"),
                eventTargets: calEvent.eventTargets,
                destination: calEvent.eventDestination,
                eventType: calEvent.eventType,
                eventDataId: calEvent.eventDataId,
                eventAttachments: calEvent.eventAttachments,
                eventItemId: calEvent.eventItemId,
                eventSubType: calEvent.eventSubType,
                eventItemDate: itemDate
            });

            showModal("_calendarEventDetailModal", {}, "#_calendarEventDetailModal")
        }
    },

    "click .collapse-expander": function (event) {
        event.stopImmediatePropagation();
        const targetKey = $(event.currentTarget).data("destination-key");
        const isClosed = ($(event.currentTarget).hasClass("fa-chevron-circle-down"));
        const handle = '*[data-source-key="' + targetKey + '"]';
        if (isClosed) {
            $(event.currentTarget).removeClass("fa-chevron-circle-down").addClass("fa-chevron-circle-up");
            $(handle).removeClass("d-none");
        } else {
            $(event.currentTarget).removeClass("fa-chevron-circle-up").addClass("fa-chevron-circle-down");
            $(handle).addClass("d-none");
        }
    }
});