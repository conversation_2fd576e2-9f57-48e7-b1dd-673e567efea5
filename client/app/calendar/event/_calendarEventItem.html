<template name="_calendarEventItem">
    <style>
        .holiday-event {
            border-radius: 6px;
            padding: 16px;
            margin-bottom: 12px;
            font-size: 1rem;
            line-height: 1.5;
            text-align: center;
        }

        .holiday-title {
            font-size: 1.2rem;
            font-weight: bold;
        }

        .holiday-subtitle {
            font-size: 1rem;
            font-weight: 500;
            margin-top: 0.5rem;
        }

        .holiday-schedule-wrapper {
            display: inline-block;
            text-align: left;
            margin-top: 0.25rem;
        }

        .holiday-schedule-list {
            list-style-type: disc;
            list-style-position: inside;
            margin: 0;
            padding: 0;
        }
    </style>
    {{#if item.isHoliday}}
        <div data-cy="calendar-event-item" class="calendar-item holiday-event text-center px-6"
             style="background-color: {{item.color}}; color: {{item.textColor}}"
             data-event="{{jsonify item}}" data-date="{{item.start}}" data-id="{{item.eventDestination}}|{{item.eventItemId}}">
            <h2 class="holiday-title font-weight-bolder">{{item.eventHeader}}: {{item.holidayData.name}}</h2>
            <div class="holiday-subtitle mt-1">Operating Schedule Types:</div>
            <div class="holiday-schedule-wrapper">
                <ul class="holiday-schedule-list">
                    {{#each item.operatingSchedules}}
                        <li>{{this}}</li>
                    {{/each}}
                </ul>
            </div>
        </div>
    {{else}}
        <div data-cy="calendar-{{classType}}-item" class="{{item.additionalStyle}} calendar-{{classType}}-item calendar-item {{#if item.eventItemId}}clickable{{/if}} {{#if item.isCurriculumTheme}}theme-header{{/if}} {{#if item.hasCurriculumThemeId}}theme-item{{/if}} {{#if item.collapsibleSourceKey}}d-none{{/if}}"
             data-event="{{jsonify item}}" data-date="{{item.start}}" data-id="{{item.eventDestination}}|{{item.eventItemId}}" data-source-key="{{item.collapsibleSourceKey}}">
            <div data-cy="calendar-group-{{item.title}}" class="d-flex justify-content-between">
                <div data-cy="calendar-item">
                    {{#if item.subheader}}
                        <div class="sub-header">{{item.subheader}}</div>
                    {{/if}}
                    {{#if item.eventTags}}
                        <div class="event-tags">
                            {{#each tag in item.eventTags}}<div class="event-tag">{{tag}}</div>{{/each}}
                        </div>
                    {{/if}}
                    {{#if showIcon}}
                        {{#if itemIcon item}}
                            <i class="fad {{itemIcon item}}"></i>
                        {{/if}}
                    {{/if}}
                    <div data-cy="title" class="title">{{item.title}}</div>
                    {{#if displaySubTitle item}}
                        <div class="sub-title">{{item.eventTargets}}</div>
                    {{/if}}
                    {{#if item.eventStats}}
                        <div class="event-stats" data-cy="event-stats">
                            {{#each stat in item.eventStats}}
                                <div class="event-stat" data-cy="event-stat">
                                    <div class="event-stat-value" data-cy="event-stat-value">{{stat.value}}</div>
                                    <div class="event-stat-name" data-cy="event-stat-name">{{stat.name}}</div>
                                </div>
                            {{/each}}
                        </div>
                    {{/if}}
                    {{#if showDetail}}
                        <p>{{item.eventDetail}}</p>
                        {{#if item.eventSubDetail}}
                            <p class="mt-2">{{item.eventSubDetail}}</p>
                        {{/if}}
                        {{#if item.eventInternalNotes}}
                            <p class="mt-2">{{item.eventInternalNotes}}</p>
                        {{/if}}
                        {{#if item.eventInternalLink}}
                            <a class="mt-2" href="{{item.eventInternalLink}}" target="_blank">View Internal Link</a>
                        {{/if}}
                    {{/if}}
                    {{#if isPrintMode}}
                        <div class="event-detail">
                            {{item.eventDetail}}
                        </div>
                    {{/if}}
                </div>
                {{#if item.collapsibleDestinationKey}}
                    <div class="align-self-center" data-cy="item-expand-btn">
                        <i class="fad fa-chevron-circle-down fa-2x collapse-expander" style="color:#fff" data-destination-key="{{item.collapsibleDestinationKey}}"></i>
                    </div>
                {{/if}}
            </div>
        </div>
    {{/if}}
</template>