import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import './_calendarDayView.html'
import './_calendarWeekView.html'
import './_calendarMonthView.html'
import moment from "moment-timezone";
import _ from 'underscore';
import { CALENDAR_EVENTS } from '../../../../lib/calendar/calendarConstants';


// Shared helpers for all view templates
const viewHelpers = {
    itemIcon(item) {
        let iconName = "";
        switch (item.eventType || item.title) {
            case CALENDAR_EVENTS.ACTIVITIES:
                iconName = "fa-graduation-cap";
                break;
            case "announcement":
            case CALENDAR_EVENTS.ANNOUNCEMENTS:
                iconName = "fa-bullhorn";
                break;
            case CALENDAR_EVENTS.FOOD:
                iconName = "fa-utensils";
                break;
            case "reservationSlot":
                break;
        }

        if (iconName !== "")
            return iconName;
    },

    jsonify(item) {
        return encodeURIComponent(JSON.stringify(item));
    },

    displaySubTitle(passedItem) {
        const item = (passedItem && passedItem.eventTargets) || [];
        return (item.length === 1 && item[0] !== passedItem.title);
    },

    isPrintMode() {
        return FlowRouter.current().queryParams['view'] === 'print';
    }
};

// Day View Helpers
Template._calendarDayView.helpers({
    ...viewHelpers,
    daySections() {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return [];
        }
        const rangeStart = service.rangeStart.get();
        const rangeEnd = service.rangeEnd.get();
        const events = service.getEvents(rangeStart, rangeEnd);

        return _.chain(events)
            .groupBy((i) => {
                return i.eventDestination;
            })
            .map((v, k) => {
                return {title: k, items: v};
            })
            .value();
    }
});

// Week View Helpers
Template._calendarWeekView.helpers({
    ...viewHelpers,
    weekSections() {
        // Find the main calendar instance to get the calendarService
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return [];
        }
        const rangeStart = service.rangeStart.get();
        const rangeEnd = service.rangeEnd.get();
        const timezone = service.timezone;

        let items

        // When entering print view, reuse the data already retrieved
        if (FlowRouter.getQueryParam('view') === 'print') {
            const calendarEvents = JSON.parse(sessionStorage.getItem('calendarEvents'));
            items = calendarEvents.filter((i) => i.eventType === FlowRouter.getQueryParam('showOnly'));
        } else {
            items = service.getEvents(rangeStart, rangeEnd);
            sessionStorage.setItem('calendarEvents', JSON.stringify(items))
        }

        const sectionHeaders = _.uniq(_.pluck(items, "eventDestination"));
        const numDates = (new moment.tz(rangeEnd, timezone).diff(new moment.tz(rangeStart, timezone), "days")) + 1;
        const dateItems = _.times(numDates, (n) => {
            return new moment.tz(rangeStart, timezone).add(n, "days").valueOf()
        });

        return sectionHeaders.map((sectionHeader) => {
            return {
                title: sectionHeader,
                days: dateItems.map((dateItem) => {
                    return {
                        date: dateItem,
                        items: items.filter((item) => {
                            const eod = item.start + (60 * 60 * 1000);
                            return item.eventDestination === sectionHeader &&
                                (item.end ?
                                        dateItem <= item.end && dateItem >= item.start :
                                        dateItem <= eod && dateItem >= item.start
                                );
                        })
                    }
                })
            }
        });
    },

    weekviewDayLabels() {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return [];
        }
        const rangeStart = service.rangeStart.get();
        const rangeEnd = service.rangeEnd.get();
        const timezone = service.timezone;

        const numDates = (new moment.tz(rangeEnd, timezone).diff(new moment.tz(rangeStart, timezone), "days")) + 1;
        return _.times(numDates, (n) => {
            return new moment.tz(rangeStart, timezone).add(n, "days").valueOf()
        });
    }
});

// Month View Helpers
Template._calendarMonthView.helpers({
    ...viewHelpers,
    monthWeeks() {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return [];
        }
        const timezone = service.timezone;
        const monthStart = new moment.tz(service.rangeStart.get(), timezone).startOf("month");
        const monthEnd = new moment.tz(service.rangeEnd.get(), timezone).endOf("month");

        const items = service.getEvents(monthStart.valueOf(), monthEnd.valueOf());
        const displayRangeStart = monthStart.subtract(monthStart.day(), "days");
        const displayRangeEnd = monthEnd.add((6 - monthEnd.day()), "days");

        let weeks = [];
        for (let n = 0; n <= displayRangeEnd.diff(displayRangeStart, "weeks"); n++) {
            let weekdays = [];
            for (let d = 0; d < 7; d++) {
                const currentDay = displayRangeStart.clone().add((n * 7) + d, "days");
                weekdays.push({
                    dayLabel: currentDay.date(),
                    items: _.filter(items, (i) => {
                        return (i.end ?
                                currentDay.valueOf() <= i.end && currentDay.valueOf() >= i.start :
                                i.start === currentDay.valueOf()
                        );
                    })
                });
            }
            weeks.push(weekdays);
        }
        return weeks;
    }
});