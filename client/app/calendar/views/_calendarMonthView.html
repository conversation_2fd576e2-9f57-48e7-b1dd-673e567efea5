<template name="_calendarMonthView">
    <div class="list-table">
        <div class="list-table-row">
            <div class="list-table-column month">
                <table class="calendar-grid-table month">
                    <tr class="calendar-grid-row">
                        <th class="calendar-grid-col">Sun</th>
                        <th class="calendar-grid-col">Mon</th>
                        <th class="calendar-grid-col">Tue</th>
                        <th class="calendar-grid-col">Wed</th>
                        <th class="calendar-grid-col">Thu</th>
                        <th class="calendar-grid-col">Fri</th>
                        <th class="calendar-grid-col">Sat</th>
                    </tr>
                    {{#each week in monthWeeks}}
                        <tr>
                            {{#each day in week}}
                                <td class="calendar-grid-col">
                                    <div class="calendar-day-block">
                                        <div class="day-label">{{day.dayLabel}}</div>
                                        <div data-cy="calendar-month-item" class="calendar-items">
                                            {{#each item in day.items}}
                                                {{> _calendarEventItem classType='month' item=item showDetail=false showIcon=true}}
                                            {{/each}}
                                        </div>
                                    </div>
                                </td>
                            {{/each}}
                        </tr>
                    {{/each}}
                </table>
            </div>
        </div>
    </div>
</template>