<template name="_calendarDayView">
    {{#each daySection in daySections}}
        <div class="list-table">
            <div class="separator separator-dashed my-4"></div>
            <div class="list-table-row">
                <div class="list-table-column row">
                    <div class="col-md-2 section-title d-flex align-items-center">
                        <i class="mr-4 fad fad-primary fa-2x {{itemIcon daySection}} section-icon"></i>
                        <span>{{daySection.title.capitalizeFirstLetter}}</span>
                    </div>
                    <div class="col-md-10">
                        {{#each item in daySection.items}}
                            {{> _calendarEventItem classType='day' item=item showDetail=true showIcon=false}}
                        {{/each}}
                    </div>
                </div>
            </div>
        </div>
    {{/each}}
    {{#unless daySections}}
        <div class="list-table">
            <div class="list-table-row">
                <div class="list-table-column text-center">
                    <h4>No items scheduled on this day.</h4>
                </div>
            </div>
        </div>
    {{/unless}}
</template>