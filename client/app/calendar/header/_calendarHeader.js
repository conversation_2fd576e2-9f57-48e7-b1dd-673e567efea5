import { Template } from 'meteor/templating';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';
import { AvailableCustomizations } from '../../../../lib/customizations';
import { CALENDAR_FILTERS, CALENDAR_VIEWS } from '../../../../lib/calendar/calendarConstants';
import { Groups } from '../../../../lib/collections/groups';
import { getShowSections } from '../../../../lib/calendar/calendarUtils';
import moment from "moment-timezone";
import './_calendarHeader.html';

Template._calendarHeader.helpers({
    groups() {
        return Groups.find({}, {sort: {name: 1}});
    },
    showGroupsFilter() {
        return (Groups.find().count() > 1 && FlowRouter.current().route.name !== "calendar/print");
    },
    showSchedulingByGroup() {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return false;
        }
        const showSections = getShowSections(service.data);
        return service.currentOrg.hasCustomization(AvailableCustomizations.RESERVATIONS_ENABLED) && showSections.includes("reservations");
    },
    dateLabel() {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return '';
        }

        const timezone = service.timezone;
        const rangeStart = service.rangeStart.get();
        const rangeEnd = service.rangeEnd.get();
        const selectedStyle = service.viewStyle.get();
        switch (selectedStyle) {
            case CALENDAR_VIEWS.DAY:
                return new moment.tz(rangeStart, timezone).format("ddd MMM D YYYY");
            case CALENDAR_VIEWS.THREE_DAY:
            case CALENDAR_VIEWS.WEEK:
                return new moment.tz(rangeStart, timezone).format("M/D/YYYY") + ' - ' + new moment.tz(rangeEnd, timezone).format("M/D/YYYY");
            case CALENDAR_VIEWS.MONTH:
                return new moment.tz(rangeStart, timezone).format("MMMM YYYY")
        }
    },
    viewStyle() {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return {style: CALENDAR_VIEWS.DAY, layoutStyle: "list"};
        }

        const selectedStyle = service.viewStyle.get();
        switch (selectedStyle) {
            case CALENDAR_VIEWS.DAY:
                return {style: CALENDAR_VIEWS.DAY, layoutStyle: "list"};
            case CALENDAR_VIEWS.THREE_DAY:
            case CALENDAR_VIEWS.WEEK:
                return {style: CALENDAR_VIEWS.WEEK, layoutStyle: "week"};
            default:
                return {style: CALENDAR_VIEWS.MONTH, layoutStyle: "grid"};
        }
    },
    isPrintMode() {
        return FlowRouter.current().queryParams['view'] === 'print';
    },
    currentFilterValue(filterName) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return '';
        }

        if (filterName === CALENDAR_FILTERS.SORT) {
            return service.reservationsSort.get();
        }
        if (filterName === CALENDAR_FILTERS.VIEW_STYLE) {
            return service.viewStyle.get();
        }
        if (filterName === CALENDAR_FILTERS.SELECTED_GROUP) {
            return service.selectedGroup.get();
        }
    }
});

Template._calendarHeader.events({
    "change #filterGroup": async function (event) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const newGroup = $(event.currentTarget).val();
        service.selectedGroup.set(newGroup);
        await service.updateFilterOptionsState();
    },

    "change select[name='view-style']": async function (event) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const timezone = service.timezone;
        const newStyle = $(event.currentTarget).val();
        const rangeStart = service.rangeStart.get();
        service.viewStyle.set(newStyle);

        switch (newStyle) {
            case CALENDAR_VIEWS.DAY:
                service.rangeEnd.set(new moment.tz(rangeStart, timezone).endOf('day').valueOf());
                break;
            case CALENDAR_VIEWS.THREE_DAY:
                service.rangeEnd.set(new moment.tz(rangeStart, timezone).add(2, 'days').endOf('day').valueOf());
                break;
            case CALENDAR_VIEWS.WEEK:
                service.rangeStart.set(new moment.tz(rangeStart, timezone).startOf('week').add(1, 'days').valueOf());
                service.rangeEnd.set(new moment.tz(rangeStart, timezone).endOf('week').subtract(1, 'days').valueOf());
                break;
            case CALENDAR_VIEWS.MONTH:
                service.rangeStart.set(new moment.tz(rangeStart, timezone).startOf('month').valueOf());
                service.rangeEnd.set(new moment.tz(rangeStart, timezone).endOf('month').valueOf());
                break;
        }
        await service.updateFilterOptionsState();
    },

    "change #filterReservationSort": async function (event) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const newSort = $(event.currentTarget).val();
        service.reservationsSort.set(newSort);
        await service.updateFilterOptionsState();
    },

    "click .move-date": function (event) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const selectedAction = $(event.currentTarget).data("action");
        const addMultiplier = selectedAction === "add" ? 1 : -1;
        service.recalculateRange(addMultiplier);
    },

    "click #btnToday": function () {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const timezone = service.timezone;
        service.rangeStart.set(new moment.tz(timezone).startOf('day').valueOf());
        service.recalculateRange(0);
    },

    "change #btnCalendar": function (event) {
        const service = window.CurrentCalendarService.get();
        if (!service) {
            return;
        }

        const timezone = service.timezone;
        const newDate = new moment.tz(event.date, timezone);
        service.rangeStart.set(newDate.startOf('day').valueOf());
        service.recalculateRange(0);
    }
});