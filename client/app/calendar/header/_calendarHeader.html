<template name="_calendarHeader">
    <div class="d-flex flex-row flex-grow-1 justify-content-between mb-6">
        <div class="d-flex flex-row align-items-center">
            <span class="mr-6">
                <span data-cy="calendar-btn" style="cursor:pointer;" id="btnCalendar"><i class="fad fad-primary fa-calendar-day icon-2x"></i></span>
            </span>
            <span data-cy="calendar-date" class="font-size-h3 font-weight-bold mr-6 min-w-200px text-center">{{dateLabel}}</span>
            <span data-cy="calendar-previous" class="move-date" style="cursor:pointer;" data-action="subtract"><i class="fad fad-primary fa-chevron-left icon-2x mr-4"></i></span>
            <span data-cy="calendar-next" class="move-date" style="cursor:pointer;" data-action="add"><i class="fad fad-primary fa-chevron-right icon-2x"></i></span>
        </div>
        <div class="d-flex flex-row align-items-center justify-content-end">
            {{#if showSchedulingByGroup}}
                <div class="mr-6">
                    <select data-cy="filter-reservation-sort" class="form-control" id="filterReservationSort">
                        <option value="" {{selectedIfEqual (currentFilterValue "reservationSort") ""}}>Alphabetical</option>
                        <option value="group" {{selectedIfEqual (currentFilterValue "reservationSort") "group"}}>Group</option>
                        <option value="schedule-type" {{selectedIfEqual (currentFilterValue "reservationSort") "schedule-type"}}>Group &amp; Schedule Type</option>
                    </select>
                </div>
            {{/if}}
            {{#unless isPrintMode}}
                {{#if showGroupsFilter}}
                    <div class="mr-6">
                        <select data-cy="calendar-filter-group" class="form-control" id="filterGroup">
                            <option value="">All Groups</option>
                            {{#each groups}}
                                <option value="{{_id}}" {{selectedIfEqual (currentFilterValue "selectedGroup") _id}}>{{name}}</option>
                            {{/each}}
                        </select>
                    </div>
                {{/if}}
                <div class="mr-6">
                    <select data-cy="calendar-view" name="view-style" class="form-control">
                        <option value="day" {{selectedIfEqual (currentFilterValue "viewStyle") "day"}}>Day View</option>
                        <option value="3day" {{selectedIfEqual (currentFilterValue "viewStyle") "3day"}}>3 Day View</option>
                        <option value="week" {{selectedIfEqual (currentFilterValue "viewStyle") "week"}}>Week View</option>
                        <option value="month" {{selectedIfEqual (currentFilterValue "viewStyle") "month"}}>Month View</option>
                    </select>
                </div>
                <div data-cy="calendar-today" class="btn btn-primary font-weight-bolder" id="btnToday">TODAY</div>
                {{#if trueIfEq viewStyle.style "week"}}
                    <div class="dropdown">
                        <button data-cy="print-pdf-btn" class="btn btn-secondary dropdown-toggle ml-4" type="button" id="dropdownMenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fad fa-print"></i>
                            Print / PDF
                        </button>
                        <div class="dropdown-menu" aria-labelledby="dropdownMenuButton">
                            <a data-cy="btn-print-menu" class="dropdown-item" id="btnPrintMenu" href="#">Menu</a>
                            <a class="dropdown-item" id="btnPrintLessons" href="#">Lesson Plan - Calendar</a>
                            <a class="dropdown-item" data-cy="print-lesson-plan-summary-btn" id="btnPrintLessonsSummary" href="#">Lesson Plan - Summary</a>
                        </div>
                    </div>
                {{/if}}
            {{/unless}}
        </div>
    </div>
</template>