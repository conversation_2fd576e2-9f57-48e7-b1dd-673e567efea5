<template name="_orgHolidayPanel">
    <div id="panelHolidays" class="collapse">

        <div class="card-body">
            <!-- Add Holiday Button -->
            <div class="mb-5">
                <button data-cy="add-holiday" id="btnAddHoliday" class="btn btn-primary font-weight-bolder">
                    <i class="fad fa-swap-opacity fa-plus text-white"></i> Add Holiday
                </button>
            </div>

            <!-- Add/Edit Holiday Form -->
            <form id="frmAddHoliday" style="display:none" class="mb-8">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">Add/Edit Holiday</h3>
                    </div>
                    <div class="card-body">
                        <input type="hidden" name="holiday-id">

                        <!-- Holiday Name -->
                        <div class="form-group row mb-4">
                            <label class="col-form-label col-lg-3 col-sm-12">Type</label>
                            <div class="col-lg-9 col-md-9 col-sm-12">
                                <input data-cy="holiday-name" type="text" class="form-control" id="AddHoliday-Name"
                                       placeholder="e.g. Memorial Day, Labor Day, etc." required>
                            </div>
                        </div>

                        <!-- Date Type and Date Fields -->
                        <div class="form-group row mb-4">
                            <label class="col-form-label col-lg-3 col-sm-12">Date Type</label>
                            <div class="col-lg-9 col-md-9 col-sm-12">
                                <select id="holidayDateType" class="form-control">
                                    <option value="individual" selected>Individual Day</option>
                                    <option value="range">Date Range</option>
                                </select>
                            </div>
                        </div>

                        <!-- Individual Date -->
                        <div class="form-group row mb-4" id="holidayDateIndividualWrapper">
                            <label class="col-form-label col-lg-3 col-sm-12">Date</label>
                            <div class="col-lg-9 col-md-9 col-sm-12">
                                <input data-cy="holiday-date" type="text" class="form-control" id="holidayDate">
                            </div>
                        </div>

                        <!-- Date Range -->
                        <div class="form-group row mb-4" id="holidayDateRangeWrapper" style="display:none">
                            <label class="col-form-label col-lg-3 col-sm-12">Date Range</label>
                            <div class="col-lg-9 col-md-9 col-sm-12">
                                <div class="row">
                                    <div class="col-md-6">
                                        <label>Start Date</label>
                                        <input type="text" class="form-control" id="holidayStartDate">
                                    </div>
                                    <div class="col-md-6">
                                        <label>End Date</label>
                                        <input type="text" class="form-control" id="holidayEndDate">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Schedule Types -->
                        <div class="form-group row mb-4">
                            <label class="col-form-label col-lg-3 col-sm-12">Schedule Types Offered</label>
                            <div class="col-lg-9 col-md-9 col-sm-12">
                                <select id="holidayScheduleTypes" class="form-control" multiple>
                                    <option value="None" selected>None - No Schedules Served</option>
                                    <option value="All">All</option>
                                </select>
                                <span class="form-text text-muted">Selecting 'None' will deselect all others. Selecting 'All' will select all schedule types.</span>
                            </div>
                        </div>

                        <!-- Auto Prorate -->
                        {{#if orgHasAutoProrateBySchedule}}
                        <div class="form-group row mb-4">
                            <label class="col-form-label col-lg-3 col-sm-12 d-flex align-items-center">
                                Auto Prorate
                                <i
                                        class="fad fad-primary fa-info-circle ml-2"
                                        data-toggle="tooltip"
                                        data-placement="right"
                                        title="For children enrolled in a Weekly Scaled Pricing Plan, if the child is scheduled on a holiday the number of days they are invoiced for will decrease by one for a single week. For children enrolled in any other billing plan, the child will be billed by the number of eligible business days within the time period being invoiced for.">
                                </i>
                            </label>
                            <div class="col-lg-9 col-md-9 col-sm-12">
                                <select id="holidayAutoProrate" class="form-control">
                                    <option value="true" selected>Yes - Autoprorate future invoice for holiday</option>
                                    <option value="false">No - Do not affect future invoice for holiday
                                    </option>
                                </select>
                            </div>
                        </div>
                        {{/if}}

                        <!-- Form Actions -->
                        <div class="form-group row">
                            <div class="col-lg-9 ml-lg-auto">
                                <button data-cy="save-add-holiday" type="submit"
                                        class="btn btn-primary font-weight-bolder mr-2" id="btnSaveAddHoliday">Save
                                </button>
                                <button type="button" class="btn btn-secondary font-weight-bolder"
                                        id="btnCancelAddHoliday">Cancel
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
            <table class="table">
                <tr style="border-bottom:1px solid #000">
                    <th>Name</th>
                    <th>Date</th>
                    <th>Permitted Schedules</th>
                    <th>Autoprorate Enabled</th>
                    <th></th>
                </tr>
                {{#each getHolidays}}
                    <tr data-cy="holiday-table">
                        <td>
                            {{name}}
                        </td>
                        <td>
                           {{displayHolidayDate this }}
                        </td>
                        <td>
                            {{getPermittedScheduleTypes this}}
                        </td>
                        <td>
                            {{getAutoProrate this}}
                        </td>
                        <td>
                            <span data-cy="edit-holiday" class="btnEditHoliday text-primary" style="cursor:pointer;"
                                  data-id="{{_id}}">Edit</span> |
                            <span data-cy="delete-holiday" class="btnDeleteHoliday text-primary" style="cursor:pointer;"
                                  data-id="{{_id}}">Delete</span>
                        </td>
                    </tr>
                {{/each}}
            </table>
        </div>
    </div>

    <div class="modal fade" id="confirmHolidayModal" tabindex="-1" role="dialog" aria-labelledby="confirmHolidayModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered" role="document">
            <div class="modal-content">
                <div class="modal-header justify-content-center">
                    <h5 class="modal-title" id="confirmHolidayModalLabel">Confirm Holiday</h5>
                </div>
                <div class="modal-body">
                    <p>You are about to save the following holiday: <strong><span id="modalHolidayName">[Holiday Name]</span></strong> on <strong><span id="modalHolidayDate">[Date]</span></strong>. By proceeding, you agree to:</p>
                    <ul>
                        <li>
                            <strong>Allow schedules of selected types:</strong> The following schedule types are allowed to occur on this holiday:
                            <ul id="modalScheduleTypesList">
                                <li>[List of selected Schedule Types]</li>
                                <li>[List of selected Schedule Types]</li>
                            </ul>
                        </li>
                        <li><strong>Cancel all existing schedules</strong> for schedule types not selected. This action cannot be undone.</li>
                        <li><strong>Prohibit the creation of new schedules</strong> with unapproved types on this holiday, after it is saved.</li>
                    </ul>
                    <p>Please confirm:</p>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" value="" id="confirmHolidayCheckbox">
                        <label class="form-check-label" for="confirmHolidayCheckbox">
                            I understand that schedules not of the selected types will be canceled and cannot be reinstated.
                        </label>
                    </div>
                </div>
                <div class="modal-footer justify-content-center">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal" id="btnConfirmCancel">Cancel</button>
                    <button type="button" class="btn btn-primary" id="btnConfirmSave" disabled>Save</button>
                </div>
            </div>
        </div>
    </div>
</template>