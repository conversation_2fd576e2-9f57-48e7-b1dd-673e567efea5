import './_orgHolidayPanel.html';
import moment from 'moment-timezone';
import $ from 'jquery';
import { AddHolidayClientService } from '../../services/admin/AddOrgHolidayClientService';
import { Orgs } from '../../../lib/collections/orgs';
import { Template } from 'meteor/templating';
import { HOLIDAY_SCHEDULE_TYPES, HOLIDAY_TYPES } from '../../../lib/constants/holidayConstants';

Template._orgHolidayPanel.onCreated(function () {
    this.service = new ReactiveVar(null);
    this.tracker = Tracker.autorun(() => {
        this.service.set(new AddHolidayClientService(Orgs.current()));
    });
});

Template._orgHolidayPanel.onDestroyed(function () {
    Template.instance().tracker.stop();
});

Template._orgHolidayPanel.onRendered(function () {
    // Populate schedule types
    const service = Template.instance().service.get();
    if (service) {
        // Initialize select2 for schedule types
        const selectList = service.getScheduleSelectList();

        selectList.select2({
            placeholder: 'Select schedule types',
            allowClear: true
        });

        service.buildScheduleTypeSelectOptions(selectList);
    }
});

Template._orgHolidayPanel.helpers({
    displayHolidayDate(holiday) {
        return Template.instance().service.get()?.displayHolidayDate(holiday);
    },
    getPermittedScheduleTypes(holiday) {
        return Template.instance().service.get()?.getPermittedScheduleTypes(holiday);
    },
    getAutoProrate(holiday) {
        return Template.instance().service.get()?.getAutoProrate(holiday);
    },
    getHolidays() {
        return Template.instance().service.get()?.getHolidays();
    },
    orgHasAutoProrateBySchedule() {
        return Template.instance().service.get()?.orgHasAutoProrateBySchedule();
    }
});

Template._orgHolidayPanel.events({
    'click #btnAddHoliday': function (e, i) {
        e.preventDefault();
        $("#frmAddHoliday").show();
        $("#btnAddHoliday").hide();
        $("input[name=holiday-id]").val("");
        $("#holidayDate").datepicker({
            autoclose: true, todayHighlight: true,
        });

        // Reset schedule types to default (None)
        i.service?.get().getScheduleSelectList().val([HOLIDAY_SCHEDULE_TYPES.NONE]).trigger('change.select2');
    },

    'click #btnCancelAddHoliday': function (e, i) {
        $("#frmAddHoliday").hide();
        $("#frmAddHoliday")[0].reset();
        $("#btnAddHoliday").show();
        // Reset schedule types to default (None)
        i.service?.get().getScheduleSelectList().val([HOLIDAY_SCHEDULE_TYPES.NONE]).trigger('change.select2');
        // Reset date type to individual
        $('#holidayDateIndividualWrapper').show();
        $('#holidayDateRangeWrapper').hide();
    },

    'click #btnSaveAddHoliday': async function (e, i) {
      e.preventDefault();

      const holidayName = $("#AddHoliday-Name").val();
      const holidayDate = new moment($("#holidayDate").val(), "MM/DD/YYYY").format("YYYY-MM-DD");
      const existingId = $("input[name=holiday-id]").val();

      if (!holidayName || holidayName.trim() === '') {
        return mpSwal.fire({ icon: "error", title: "Cannot save empty field", text: "Please enter a holiday name" });
      }
      if (!holidayDate || holidayDate.trim() === '') {
        return mpSwal.fire({ icon: "error", title: "Invalid date", text: "Please enter a valid date for the holiday" });
      }

      const service = i.service.get();
      const holidayObject = service?.generateHolidayObject();

      // If editing an existing holiday, show confirmation popup
      if (existingId) {
        // Find the original holiday data
        const originalHoliday = Orgs.current().getHolidays().find((holiday) => { return holiday._id == existingId; });

        if (originalHoliday) {
          const originalDate = new moment(originalHoliday.date, "YYYY-MM-DD").format("MM/DD/YYYY");
          const newDate = new moment(holidayDate, "YYYY-MM-DD").format("MM/DD/YYYY");

          const confirmationHtml = `
            <div style="text-align: left; margin: 20px 0;">
              <p>You are about to edit the holiday: <strong>${originalHoliday.name}</strong> initially set for <strong>${originalDate}</strong>. This holiday will be updated to <strong>${holidayName}</strong> on <strong>${newDate}</strong>.</p>
              
              <p>By proceeding, you understand:</p>
              <ul style="margin-left: 20px;">
                <li>Updating the schedule date will not reinstate previously canceled schedules.</li>
                <li>Modifying allowed schedule types may result in additional schedule cancellations.</li>
                <li>New schedule creation will be prohibited for unapproved schedule types on this holiday upon saving.</li>
              </ul>
              
              <p>Please confirm:</p>
              <div style="margin: 15px 0;">
                <label style="display: flex; align-items: center; cursor: pointer;">
                  <input data-cy="holiday-confirmation-checkbox" type="checkbox" id="holidayEditConfirmation" style="margin-right: 10px;">
                  <span>I understand that schedule adjustments may need to be made manually to accommodate my edits.</span>
                </label>
              </div>
            </div>
          `;

          const result = await mpSwal.fire({
            title: "Editing Holiday",
            html: confirmationHtml,
            showCancelButton: true,
            confirmButtonText: 'Save',
            cancelButtonText: 'Cancel',
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            preConfirm: () => {
              const checkbox = document.getElementById('holidayEditConfirmation');
              if (!checkbox.checked) {
                mpSwal.showValidationMessage('You must confirm that you understand the implications before proceeding.');
                return false;
              }
              return true;
            },
            didOpen: () => {
              const confirmButton = mpSwal.getConfirmButton();
              const checkbox = document.getElementById('holidayEditConfirmation');
              confirmButton.setAttribute('data-cy', 'holiday-edit-save-btn');

              // Initially disable the save button
              confirmButton.disabled = true;
              confirmButton.style.opacity = '0.6';
              confirmButton.style.cursor = 'not-allowed';

              // Enable/disable save button based on checkbox state
              checkbox.addEventListener('change', function() {
                if (this.checked) {
                  confirmButton.disabled = false;
                  confirmButton.style.opacity = '1';
                  confirmButton.style.cursor = 'pointer';
                } else {
                  confirmButton.disabled = true;
                  confirmButton.style.opacity = '0.6';
                  confirmButton.style.cursor = 'not-allowed';
                }
              });
            }
          });

          if (!result.isConfirmed) {
            return; // User cancelled
          }

          // For editing: use the service method and return early
          await service?.addUpdateHoliday(holidayObject);
          $("#frmAddHoliday").hide();
          $("#frmAddHoliday")[0].reset();
          $("#btnAddHoliday").show();
          return;
        }
      }

      // For new holidays: Show confirmation modal
      $("#modalHolidayName").text(holidayObject.name);
      $("#modalHolidayDate").text(service?.displayHolidayDate(holidayObject));

      // Update schedule types list in modal
      const scheduleTypesList = $("#modalScheduleTypesList");
      scheduleTypesList.empty();
      if (holidayObject.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.ALL)) {
        scheduleTypesList.append('<li>All Schedule Types</li>');
      } else if (holidayObject.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.NONE)) {
        scheduleTypesList.append('<li>No Schedule Types</li>');
      } else {
        const allScheduleTypes = service?.getScheduleTypes();
        holidayObject.scheduleTypes.forEach(type => {
          const match = allScheduleTypes.find(st => st._id === type);
          scheduleTypesList.append(`<li>${match?.type || type}</li>`);
        });
      }

      // Show modal and handle confirmation
      $("#confirmHolidayModal").modal('show');
      $("#confirmHolidayCheckbox").prop('checked', false);
      $("#btnConfirmSave").prop('disabled', true);
    },

    'click #confirmHolidayCheckbox': function(e) {
        $("#btnConfirmSave").prop('disabled', !e.target.checked);
    },

    'click #btnConfirmSave': async function(e, i) {
        const holidayObject = i.service.get()?.generateHolidayObject();

        const validationError = await i.service.get()?.validateHoliday(holidayObject);

        if (validationError) {
            return mpSwal.fire({
                icon: "error",
                title: 'Validation Error',
                text: validationError
            });
        }

        await i.service.get()?.addUpdateHoliday(holidayObject);
    },

    'click #btnConfirmCancel': function() {
        $("#confirmHolidayModal").modal('hide');
    },

    'click .btnEditHoliday': function (e, i) {
        e.preventDefault();

        $("input[name=holiday-id]").val(this._id);
        $("#AddHoliday-Name").val(this.name);

        // Set date type and show/hide appropriate date fields
        $("#holidayDateType").val(this.dateType || HOLIDAY_TYPES.INDIVIDUAL).trigger('change');

        // Set date fields based on date type
        if (this.dateType === HOLIDAY_TYPES.RANGE) {
            // Initialize datepickers if not already initialized
            if (!$('#holidayStartDate').data('datepicker')) {
                $('#holidayStartDate').datepicker({
                    autoclose: true,
                    todayHighlight: true
                });
            }
            if (!$('#holidayEndDate').data('datepicker')) {
                $('#holidayEndDate').datepicker({
                    autoclose: true,
                    todayHighlight: true
                });
            }
            // Set range dates
            $('#holidayStartDate').datepicker('update', moment(this.startDate).format('MM/DD/YYYY'));
            $('#holidayEndDate').datepicker('update', moment(this.endDate).format('MM/DD/YYYY'));
        } else {
            // Set individual date
            $("#holidayDate").datepicker({
                autoclose: true,
                todayHighlight: true
            }).datepicker('update', moment(this.date).format('MM/DD/YYYY'));
        }

        // Set schedule types
        const scheduleTypes = this.scheduleTypes || [HOLIDAY_SCHEDULE_TYPES.NONE];
        i.service?.get().getScheduleSelectList().val(scheduleTypes).trigger('change');

        // Set auto prorate
        const autoProrate = this.autoProrate == null ? true : this.autoProrate;
        $("#holidayAutoProrate").val(String(autoProrate)).trigger('change');

        // Show form
        $("#frmAddHoliday").show();
        $("#btnAddHoliday").hide();
    },
    'click .btnDeleteHoliday': async function (e, i) {
      e.preventDefault();
      const holidayId = this._id;
      const holidayName = this.name;

      let holidayDateDisplay;
      if (this.dateType === HOLIDAY_TYPES.RANGE) {
        const startDate = new moment(this.startDate, "YYYY-MM-DD").format("MM/DD/YYYY");
        const endDate = new moment(this.endDate, "YYYY-MM-DD").format("MM/DD/YYYY");
        holidayDateDisplay = `${startDate} - ${endDate}`;
      } else {
        holidayDateDisplay = new moment(this.date, "YYYY-MM-DD").format("MM/DD/YYYY");
      }

      const confirmationHtml = `
        <div style="text-align: left; margin: 20px 0;">
          <p>You are about to delete the holiday: <strong>${holidayName}</strong> initially set for <strong>${holidayDateDisplay}</strong>. This holiday will be deleted.</p>
          
          <p>By proceeding, you understand:</p>
          <ul style="margin-left: 20px;">
            <li>Deleting this holiday will not reinstate previously canceled schedules.</li>
            <li>New schedule creation will support all schedule types upon saving.</li>
          </ul>
          
          <p>Please confirm:</p>
          <div style="margin: 15px 0;">
            <label style="display: flex; align-items: center; cursor: pointer;">
              <input data-cy="holiday-delete-confirmation-checkbox" type="checkbox" id="holidayDeleteConfirmation" style="margin-right: 10px;">
              <span>I understand I am deleting this schedule which may require me to manually update schedules to accommodate my new deletion.</span>
            </label>
          </div>
        </div>
      `;

      const result = await mpSwal.fire({
        title: "Deleting Holiday",
        html: confirmationHtml,
        showCancelButton: true,
        confirmButtonText: 'Delete',
        cancelButtonText: 'Cancel',
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        preConfirm: () => {
          const checkbox = document.getElementById('holidayDeleteConfirmation');
          if (!checkbox.checked) {
            mpSwal.showValidationMessage('You must confirm that you understand the implications before proceeding.');
            return false;
          }
          return true;
        },
        didOpen: () => {
          const confirmButton = mpSwal.getConfirmButton();
          const checkbox = document.getElementById('holidayDeleteConfirmation');

          // Add data-cy attribute to the confirm button
          confirmButton.setAttribute('data-cy', 'holiday-delete-save-btn');

          // Initially disable the delete button
          confirmButton.disabled = true;
          confirmButton.style.opacity = '0.6';
          confirmButton.style.cursor = 'not-allowed';

          // Enable/disable delete button based on checkbox state
          checkbox.addEventListener('change', function() {
            if (this.checked) {
              confirmButton.disabled = false;
              confirmButton.style.opacity = '1';
              confirmButton.style.cursor = 'pointer';
            } else {
              confirmButton.disabled = true;
              confirmButton.style.opacity = '0.6';
              confirmButton.style.cursor = 'not-allowed';
            }
          });
        }
      });

      if (result.isConfirmed) {
        await i.service.get().deleteHoliday(holidayId);
      }
    },

    'change #holidayDateType': function(e) {
        const dateType = $(e.target).val();
        if (dateType === 'individual') {
            $('#holidayDateIndividualWrapper').show();
            $('#holidayDateRangeWrapper').hide();
        } else {
            $('#holidayDateIndividualWrapper').hide();
            $('#holidayDateRangeWrapper').show();

            // Initialize datepickers for range fields if not already initialized
            if (!$('#holidayStartDate').data('datepicker')) {
                $('#holidayStartDate').datepicker({
                    autoclose: true,
                    todayHighlight: true
                });
            }
            if (!$('#holidayEndDate').data('datepicker')) {
                $('#holidayEndDate').datepicker({
                    autoclose: true,
                    todayHighlight: true
                });
            }
        }
    },

    'change #holidayScheduleTypes': function (e, i) {
        i.service.get()?.handleSelectListChange();
    }
});