import { DEFAULT_SHOW_SECTIONS } from './calendarConstants';
import { FlowRouter } from 'meteor/ostrio:flow-router-extra';

/**
 * Gets the sections to show in the calendar based on query parameters or data configuration.
 *
 * @param {Object} [data] - The data object that may contain show configuration
 * @param {string} [data.show] - Comma-separated string of sections to show
 * @returns {string[]|string} Array of section names to display, or string if from showOnly param
 *
 * @example
 * // With showOnly query parameter
 * FlowRouter.setQueryParam('showOnly', 'activities,food');
 * getShowSections({}); // Returns 'activities,food'
 *
 * @example
 * // With data.show property
 * getShowSections({ show: 'reservations,announcements' }); // Returns ['reservations', 'announcements']
 *
 * @example
 * // Fallback to defaults
 * getShowSections({}); // Returns DEFAULT_SHOW_SECTIONS
 */
export function getShowSections(data) {
    // If there's a "show-only" query param, filter sections shown
    if (FlowRouter.getQueryParam('showOnly')) {
        return FlowRouter.getQueryParam('showOnly');
    }
    return data?.show?.split(',') || DEFAULT_SHOW_SECTIONS;
}

/**
 * Formats an array of group names into a comma-separated string.
 *
 * @param {string[]} groups - Array of group names to format
 * @returns {string} Comma-separated string of group names
 *
 * @example
 * formatGroups(['Group A', 'Group B', 'Group C']); // Returns 'Group A, Group B, Group C'
 *
 * @example
 * formatGroups(['Single Group']); // Returns 'Single Group'
 *
 * @example
 * formatGroups([]); // Returns ''
 */
export function formatGroups(groups) {
    let outputString = "";

    for (let i = 0; i < groups.length; i++) {
        if (i > 0) {
            outputString += ", ";
        }

        outputString += groups[i];
    }

    return outputString;
}

/**
 * Checks if a document matches a MongoDB-style query object.
 * Supports various MongoDB operators including $or, $elemMatch, $in, $gte, $lte.
 *
 * @param {Object} doc - The document to test against the query
 * @param {Object} query - MongoDB-style query object
 * @param {Array} [query.$or] - Array of alternative query conditions (at least one must match)
 * @param {Object} [query.$elemMatch] - Matches documents that contain an array field with at least one element that matches all the specified query criteria
 * @param {*} [query.$in] - Matches any of the values specified in an array
 * @param {*} [query.$gte] - Matches values that are greater than or equal to a specified value
 * @param {*} [query.$lte] - Matches values that are less than or equal to a specified value
 * @returns {boolean} True if the document matches the query, false otherwise
 *
 * @example
 * // Simple equality match
 * const doc = { name: 'John', age: 25 };
 * matchesQuery(doc, { name: 'John' }); // Returns true
 * matchesQuery(doc, { name: 'Jane' }); // Returns false
 *
 * @example
 * // Range queries
 * const doc = { age: 25, score: 85 };
 * matchesQuery(doc, { age: { $gte: 18 } }); // Returns true
 * matchesQuery(doc, { score: { $lte: 90, $gte: 80 } }); // Returns true
 *
 * @example
 * // $in operator
 * const doc = { category: 'electronics' };
 * matchesQuery(doc, { category: { $in: ['electronics', 'books'] } }); // Returns true
 *
 * @example
 * // $or operator
 * const doc = { name: 'John', age: 25 };
 * matchesQuery(doc, { $or: [{ name: 'John' }, { age: 30 }] }); // Returns true
 *
 * @example
 * // $elemMatch operator
 * const doc = { scores: [75, 88, 92] };
 * matchesQuery(doc, { scores: { $elemMatch: { $gte: 80, $lte: 90 } } }); // Returns true
 *
 * @example
 * // Array field with $in operator
 * const doc = { tags: ['javascript', 'node', 'react'] };
 * matchesQuery(doc, { tags: { $in: ['react', 'vue'] } }); // Returns true
 */
export function matchesQuery(doc, query) {
    for (const key in query) {
        const condition = query[key];

        // Handle $or separately
        if (key === '$or') {
            const orMatched = condition.some(subQuery => matchesQuery(doc, subQuery));
            if (!orMatched) return false;
            continue;
        }

        const value = doc[key];

        // Check if the field exists in the document
        if (!(key in doc)) {
            // Field doesn't exist in document - this should never match
            // In MongoDB, missing fields don't match queries for undefined/null
            return false;
        }

        if (typeof condition === 'object' && condition !== null && !Array.isArray(condition)) {
            // Handle $elemMatch
            if ('$elemMatch' in condition) {
                if (!Array.isArray(value)) return false;
                const match = value.some(item => {
                    return Object.entries(condition['$elemMatch']).every(([op, expected]) => {
                        if (op === '$gte') return item >= expected;
                        if (op === '$lte') return item <= expected;
                        return false;
                    });
                });
                if (!match) return false;
                continue;
            }

            // Handle $in, $gte, $lte
            for (const op in condition) {
                const expected = condition[op];
                if (op === '$gte' && (value === undefined || value === null || value < expected)) return false;
                if (op === '$lte' && (value === undefined || value === null || value > expected)) return false;
                if (op === '$in') {
                    if (!Array.isArray(value)) {
                        if (!expected.includes(value)) return false;
                    } else {
                        const hasOverlap = value.some(v => expected.includes(v));
                        if (!hasOverlap) return false;
                    }
                }
            }
        } else {
            // Direct equality - need to handle arrays and objects properly
            if (!deepEqual(value, condition)) return false;
        }
    }
    return true;
}

/**
 * Deep equality comparison for arrays, objects, and primitives
 * @param {*} a - First value to compare
 * @param {*} b - Second value to compare
 * @returns {boolean} True if values are deeply equal
 */
function deepEqual(a, b) {
    // Strict equality check (handles primitives, null, undefined)
    if (a === b) return true;

    // Handle null/undefined cases
    if (a == null || b == null) return a === b;

    // Check if both are arrays
    if (Array.isArray(a) && Array.isArray(b)) {
        if (a.length !== b.length) return false;
        for (let i = 0; i < a.length; i++) {
            if (!deepEqual(a[i], b[i])) return false;
        }
        return true;
    }

    // Check if both are objects (but not arrays)
    if (typeof a === 'object' && typeof b === 'object' && !Array.isArray(a) && !Array.isArray(b)) {
        const keysA = Object.keys(a);
        const keysB = Object.keys(b);

        if (keysA.length !== keysB.length) return false;

        for (const key of keysA) {
            if (!keysB.includes(key)) return false;
            if (!deepEqual(a[key], b[key])) return false;
        }
        return true;
    }

    // Different types or other cases
    return false;
}