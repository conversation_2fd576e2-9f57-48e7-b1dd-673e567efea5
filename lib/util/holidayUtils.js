import { HOLIDAY_SCHEDULE_TYPES, HOLIDAY_TYPES } from '../constants/holidayConstants';
import moment from 'moment-timezone';

export class HolidayUtils {
    /**
     * Gets holidays that affect the given date range
     * @param {number} rangeStart - Start of range in milliseconds
     * @param {number} rangeEnd - End of range in milliseconds
     * @param {Object} org - Organization object
     * @returns {Object} Map of date (YYYY-MM-DD) to holiday data
     */
    static getHolidaysInRange(rangeStart, rangeEnd, org) {
        const holidays = org.getHolidays(true); // Allow past holidays
        if (!holidays || !Array.isArray(holidays) || holidays.length === 0) {
            return {};
        }

        const timezone = org.getTimezone();
        const holidayMap = {};

        holidays.forEach(holiday => {
            const holidayType = holiday.dateType || HOLIDAY_TYPES.INDIVIDUAL;

            if (holidayType === HOLIDAY_TYPES.INDIVIDUAL) {
                const holidayDate = moment.tz(holiday.date, timezone);
                const dateKey = holidayDate.format('YYYY-MM-DD');
                const holidayTimestamp = holidayDate.valueOf();

                if (holidayTimestamp >= rangeStart && holidayTimestamp <= rangeEnd) {
                    holidayMap[dateKey] = holiday;
                }
            } else if (holidayType === HOLIDAY_TYPES.RANGE) {
                const startDate = moment.tz(holiday.startDate, timezone);
                const endDate = moment.tz(holiday.endDate, timezone);

                let currentDate = startDate.clone();
                while (currentDate.isSameOrBefore(endDate, 'day')) {
                    const dateKey = currentDate.format('YYYY-MM-DD');
                    const currentTimestamp = currentDate.valueOf();

                    if (currentTimestamp >= rangeStart && currentTimestamp <= rangeEnd) {
                        holidayMap[dateKey] = holiday;
                    }
                    currentDate.add(1, 'day');
                }
            }
        });

        return holidayMap;
    }

    /**
     * Determines which schedule types are permitted on a holiday
     * @param {Object} holiday - The holiday object
     * @returns {Array|string} Array of permitted schedule type IDs, or 'All' or 'None'
     */
    static getPermittedScheduleTypes(holiday) {
        if (!holiday.scheduleTypes) {
            return HOLIDAY_SCHEDULE_TYPES.NONE; // Default behavior for legacy holidays
        }

        if (holiday.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.ALL)) {
            return HOLIDAY_SCHEDULE_TYPES.ALL;
        }

        if (holiday.scheduleTypes.includes(HOLIDAY_SCHEDULE_TYPES.NONE)) {
            return HOLIDAY_SCHEDULE_TYPES.NONE;
        }

        return holiday.scheduleTypes;
    }

    /**
     * Checks if a schedule type is permitted on a given holiday
     * @param {string} scheduleTypeId - The schedule type ID to check
     * @param {Object} holiday - The holiday object
     * @returns {boolean} True if the schedule type is permitted
     */
    static isScheduleTypePermitted(scheduleTypeId, holiday) {
        const permittedTypes = this.getPermittedScheduleTypes(holiday);

        if (permittedTypes === 'All') {
            return true;
        }

        if (permittedTypes === 'None') {
            return false;
        }

        return permittedTypes.includes(scheduleTypeId || '');
    }
}