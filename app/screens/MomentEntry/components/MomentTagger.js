import React from 'react';
import {ScrollView} from 'react-native';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>er,
  Body,
  Title,
  Text,
  Left,
  Right,
  Item,
  Input,
  Icon,
} from 'native-base';
import colors from '../../../config/colors.json';
import {CommonActions} from '@react-navigation/native';
import {TagSection} from './TagSection';
import {TagItem} from './TagItem';
import Nucleo from '../../../components/icons/Nucleo';
import {withTranslation} from "react-i18next";
class MomentTagger extends React.Component {
  constructor(props) {
    super(props);

    this.state = {
      taggedPeople: this.props.route?.params?.taggedPeople ?? [],
      showTaggedPeople: true,
      searchText: '',
      availableSections: [],
    };
  }
  componentDidMount() {
    const {t} = this.props;
    const {people, groups, checkInNotRequired, userPerson, currentOrg} =
      this.props.route?.params;
    const searchTerm = this.state.searchText.toLowerCase();
    const availablePeople = people.filter(p => {
      return (
        !this.state.taggedPeople.includes(p._id) &&
        (p.checkedIn || checkInNotRequired) &&
        (searchTerm === '' ||
          p?.firstName?.toLowerCase?.()?.includes?.(searchTerm) ||
          p?.lastName?.toLowerCase?.()?.includes?.(searchTerm))
      );
    });
    const allGroups = groups.filter(g => g._id !== userPerson?.checkInGroupId)
    const formattedGroups = allGroups.map(g => {
      return {
        tagId: `group|${g?._id}`,
        tagLabel: `Group: ${g?.name}`,
      }
    })
    const userGroup = groups.filter(g => g?._id === userPerson?.checkInGroupId)[0]
    const availableSections = [
      {
        index: 0,
        Title: `${t('momentEntry.checkedIn')}: ${userGroup?.name}`,
        tagId: `group|${userGroup?._id}`,
        items: [],
      },
      {
        index: 1,
        Title: `${t('momentEntry.checkedOut')}: ${userGroup?.name}`,
        tagId: `group|${userGroup?._id}`,
        items: [],
      },
      {
        index: 2,
        Title: `${t('momentEntry.checkedIn')}: ${currentOrg?.name}`,
        tagId: `org|${currentOrg?._id}`,
        items: [],
      },
      {
        index: 3,
        Title: `${t('momentEntry.checkedOut')}: ${currentOrg?.name}`,
        tagId: `org|${currentOrg?._id}`,
        items: [],
      },
    ];

    availablePeople.forEach(person => {
      let listItem = {
        tagLabel: `${person.firstName} ${person.lastName}`,
        tagId: person._id,
      };
      if (person.checkedIn) {
        if (person?.checkInGroupId === userPerson?.checkInGroupId) {
          availableSections[0].items.push(listItem);
        } else {
          availableSections[2].items.push(listItem);
        }
      } else {
        if (
          person?.defaultGroupId &&
          person.defaultGroupId === userPerson?.checkInGroupId
        ) {
          availableSections[1].items.push(listItem);
        } else {
          availableSections[3].items.push(listItem);
        }
      }
    });
    availableSections.forEach(section => {
      section?.items?.sort((a, b) => a.tagLabel.localeCompare(b.tagLabel));
    });
    availableSections[2].items.unshift(...formattedGroups.sort((a, b) => a.tagLabel.localeCompare(b.tagLabel)));
    availableSections.unshift({
      Title: 'All Staff',
      tagId: 'all',
      items: [
        {
          _id: 'role|all_staff',
          tagLabel: 'All Staff',
          tagId: 'role|all_staff',
        },
      ],
    });
    this.setState({availableSections});
  }

  UNSAFE_componentWillMount() {
    // safety check for persistence refresh
    const modalDismiss = this.props.route?.params?.onModalDismiss ?? null;
    if (!modalDismiss) this.props.navigation.dispatch(CommonActions.goBack());
  }

  onConfirm() {
    this.props.onSave({});
  }

  addPerson(personId) {
    if (!this.state.taggedPeople.includes(personId)) {
      const joined = this.state.taggedPeople.concat(personId);
      this.setState({taggedPeople: joined});
    }
  }

  removePerson(personId) {
    const joined = this.state.taggedPeople.filter(tpid => {
      return tpid !== personId;
    });
    this.setState({taggedPeople: joined});
  }

  searchPeople(searchText) {
    this.setState({searchText});
  }

  componentWillUnmount() {
    const modalDismiss = this.props.route?.params?.onModalDismiss ?? null;
    if (modalDismiss) {
      modalDismiss(this.state.taggedPeople);
    }
  }

  render() {
    const {t} = this.props;
    const {availableSections, taggedPeople, searchText} = this.state;
    return (
      <Container>
        <Header style={{backgroundColor: colors.white}}>
          <Left style={{flex: 1}}>
            <Button
              transparent
              onPress={() => {
                this.props.navigation.dispatch(CommonActions.goBack());
              }}>
              <Nucleo name="icon-arrow-left"  color={colors.primaryA}  size={24} />
            </Button>
          </Left>
          <Body
            style={{flex: 1, justifyContent: 'center', alignItems: 'center'}}>
            <Title style={{color: colors.primaryA}}>{t('momentEntry.tagPeople')}</Title>
          </Body>
          <Right style={{flex: 1}}>
            <Button
              transparent
              onPress={() => {
                this.props.navigation.dispatch(CommonActions.goBack());
              }}>
              <Text
                style={{marginLeft: 6, fontSize: 16, color: colors.primaryA}}>
                {t('actions.done')}
              </Text>
            </Button>
          </Right>
        </Header>
        <Item>
          <Icon name="search" type="MaterialIcons"/>


          <Input
            placeholder={t('people.search')}
            clearButtonMode="always"
            onChangeText={searchText => this.setState({searchText})}
          />
        </Item>
        <ScrollView
          contentContainerStyle={{
            marginTop: 20,
            paddingBottom: 40,
          }}>
          {taggedPeople.map(personId => {
            try {
              if (personId.startsWith('role|')) {
                if (personId === 'role|all_staff') {
                  return (
                    <TagItem
                      key={personId}
                      selected={true}
                      item={{
                        tagId: personId,
                        tagLabel: 'All Staff',
                      }}
                      onTag={this.removePerson.bind(this)}
                    />
                  );
                }
              } else if (personId.startsWith('group|')){
                const group = this.props.route.params.groups.find(
                  g => g?._id === personId.split('|')[1],
                );
                if (!group) {
                  console.log(`Group not found for id: ${personId}`);
                  return null;
                }
                return (
                  <TagItem
                    key={personId}
                    selected={true}
                    item={{
                      tagId: personId,
                      tagLabel: `Group: ${group?.name}`,
                    }}
                    onTag={this.removePerson.bind(this)}
                  />
                );
              } else {
                const person = this.props.route.params.people.find(
                  p => p?._id === personId,
                );

                if (!person || !person._id) {
                  console.log(`Person not found for id: ${personId}`);
                  return null;
                }
                return (
                  <TagItem
                    key={person._id}
                    selected={true}
                    item={{
                      tagId: person._id,
                      tagLabel: `${person.firstName} ${person.lastName}`,
                    }}
                    onTag={this.removePerson.bind(this)}
                  />
                );
              }
            } catch (e) {
              console.log(`Error tagging person with id: ${personId}`);
              return null;
            }
          })}
          {availableSections.map(section => {
            if (searchText !== '') {
              const filteredItems = section.items.filter(item =>
                item.tagLabel.match(searchText),
              );
              return (
                <TagSection
                  key={section.Title}
                  title={section.Title}
                  taggedPeople={taggedPeople}
                  searchText={searchText}
                  items={filteredItems}
                  onTag={this.addPerson.bind(this)}
                />
              );
            } else {
              return (
                <TagSection
                  key={section.Title}
                  title={section.Title}
                  taggedPeople={taggedPeople}
                  searchText={searchText}
                  items={section.items}
                  onTag={this.addPerson.bind(this)}
                />
              );
            }
          })}
        </ScrollView>
      </Container>
    );
  }
}

export default withTranslation()(MomentTagger);
